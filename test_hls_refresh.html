<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLS Playlist Refresh Test</title>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1b1b1b;
            color: #e0e0e0;
            margin: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #2a2a2a;
            border: 1px solid #4a4a4a;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .video-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .video-box {
            flex: 1;
            background: #333;
            border-radius: 8px;
            padding: 15px;
        }
        
        video {
            width: 100%;
            max-width: 500px;
            height: auto;
            background: #000;
            border-radius: 4px;
        }
        
        .logs {
            background: #1a1a1a;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-info { color: #87CEEB; }
        .log-success { color: #90EE90; }
        .log-warning { color: #FFD700; }
        .log-error { color: #FF6B6B; }
        
        .controls {
            margin: 15px 0;
        }
        
        button {
            background: #FC9546;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        button:hover {
            background: #e8843d;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status-running { background: #2d5a2d; color: #90EE90; }
        .status-success { background: #2d4a2d; color: #90EE90; }
        .status-error { background: #5a2d2d; color: #FF6B6B; }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .metric {
            background: #333;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #FC9546;
        }
        
        .metric-label {
            font-size: 12px;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 HLS Playlist Auto-Refresh Live Test</h1>
        
        <div class="test-section">
            <h2>📊 Test Status</h2>
            <div id="testStatus" class="status">Ready to test</div>
            
            <div class="metrics">
                <div class="metric">
                    <div id="refreshCount" class="metric-value">0</div>
                    <div class="metric-label">Playlist Refreshes</div>
                </div>
                <div class="metric">
                    <div id="segmentCount" class="metric-value">0</div>
                    <div class="metric-label">Segments Detected</div>
                </div>
                <div class="metric">
                    <div id="errorCount" class="metric-value">0</div>
                    <div class="metric-label">Errors</div>
                </div>
                <div class="metric">
                    <div id="testDuration" class="metric-value">0s</div>
                    <div class="metric-label">Test Duration</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🎬 HLS Video Player Test</h2>
            
            <div class="controls">
                <button id="startTest" onclick="startHLSTest()">🚀 Start HLS Refresh Test</button>
                <button id="stopTest" onclick="stopHLSTest()" disabled>⏹️ Stop Test</button>
                <button onclick="clearLogs()">🗑️ Clear Logs</button>
            </div>
            
            <div class="video-container">
                <div class="video-box">
                    <h3>📺 HLS Video Player</h3>
                    <video id="testVideo" controls muted></video>
                    <div id="videoInfo" style="margin-top: 10px; font-size: 12px; color: #ccc;">
                        No video loaded
                    </div>
                </div>
                
                <div class="video-box">
                    <h3>📋 Live Test Logs</h3>
                    <div id="testLogs" class="logs">
                        <div class="log-entry log-info">Ready to start HLS playlist refresh test...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>⚙️ Test Configuration</h2>
            <p><strong>HLS Stream URL:</strong> <span id="hlsUrl">Will be set when test starts</span></p>
            <p><strong>Refresh Interval:</strong> 3 seconds</p>
            <p><strong>Expected Behavior:</strong> Playlist should refresh automatically every 3 seconds, detecting new segments as they become available</p>
        </div>
    </div>

    <script>
        let hls = null;
        let testStartTime = null;
        let refreshCount = 0;
        let segmentCount = 0;
        let errorCount = 0;
        let testInterval = null;
        let durationInterval = null;
        
        const testVideo = document.getElementById('testVideo');
        const testLogs = document.getElementById('testLogs');
        const testStatus = document.getElementById('testStatus');
        const startBtn = document.getElementById('startTest');
        const stopBtn = document.getElementById('stopTest');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            testLogs.appendChild(logEntry);
            testLogs.scrollTop = testLogs.scrollHeight;
        }
        
        function updateMetrics() {
            document.getElementById('refreshCount').textContent = refreshCount;
            document.getElementById('segmentCount').textContent = segmentCount;
            document.getElementById('errorCount').textContent = errorCount;
            
            if (testStartTime) {
                const duration = Math.floor((Date.now() - testStartTime) / 1000);
                document.getElementById('testDuration').textContent = duration + 's';
            }
        }
        
        function startHLSTest() {
            // Use the existing HLS stream from the application
            const hlsStreamUrl = '/api/video/hls/zmt-compressed-video/output/big_buck_bunny_360p24/playlist.m3u8';
            
            log('🚀 Starting HLS playlist refresh test...', 'info');
            log(`📡 HLS Stream URL: ${hlsStreamUrl}`, 'info');
            
            document.getElementById('hlsUrl').textContent = hlsStreamUrl;
            
            if (!Hls.isSupported()) {
                log('❌ HLS.js is not supported in this browser', 'error');
                testStatus.innerHTML = 'HLS.js not supported';
                testStatus.className = 'status status-error';
                return;
            }
            
            // Initialize test metrics
            testStartTime = Date.now();
            refreshCount = 0;
            segmentCount = 0;
            errorCount = 0;
            
            // Create HLS instance with same config as main app
            hls = new Hls({
                debug: false,
                enableWorker: true,
                lowLatencyMode: false,
                backBufferLength: 90,
                maxBufferLength: 600,
                maxMaxBufferLength: 1200,
                manifestLoadingMaxRetry: 10,
                levelLoadingMaxRetry: 10,
                fragLoadingMaxRetry: 10,
                manifestLoadingRetryDelay: 1000,
                autoStartLoad: true
            });
            
            // Set up event listeners
            hls.on(Hls.Events.MANIFEST_PARSED, function(event, data) {
                refreshCount++;
                segmentCount = data.levels[0]?.details?.fragments?.length || 0;
                
                log(`✅ Playlist refreshed! Segments found: ${segmentCount}`, 'success');
                log(`📊 Total refreshes: ${refreshCount}`, 'info');
                
                updateMetrics();
                
                // Auto-play
                testVideo.play().catch(e => {
                    log('⚠️ Auto-play prevented by browser policy', 'warning');
                });
            });
            
            hls.on(Hls.Events.ERROR, function(event, data) {
                errorCount++;
                log(`❌ HLS Error: ${data.type} - ${data.details}`, 'error');
                updateMetrics();
                
                if (data.fatal) {
                    log('💀 Fatal error encountered', 'error');
                    testStatus.innerHTML = 'Fatal HLS Error';
                    testStatus.className = 'status status-error';
                }
            });
            
            hls.on(Hls.Events.FRAG_LOADED, function(event, data) {
                log(`📦 Segment loaded: ${data.frag.relurl}`, 'info');
            });
            
            // Load the stream
            hls.loadSource(hlsStreamUrl);
            hls.attachMedia(testVideo);
            
            // Set up automatic playlist refresh (same as main app)
            testInterval = setInterval(() => {
                log('🔄 Forcing playlist refresh...', 'info');
                hls.loadSource(hlsStreamUrl);
            }, 3000);
            
            // Update duration counter
            durationInterval = setInterval(updateMetrics, 1000);
            
            // Update UI
            startBtn.disabled = true;
            stopBtn.disabled = false;
            testStatus.innerHTML = 'Test Running - Monitoring playlist refreshes...';
            testStatus.className = 'status status-running';
            
            log('✅ Test started! Monitoring playlist refreshes every 3 seconds...', 'success');
        }
        
        function stopHLSTest() {
            log('⏹️ Stopping HLS test...', 'warning');
            
            if (testInterval) {
                clearInterval(testInterval);
                testInterval = null;
            }
            
            if (durationInterval) {
                clearInterval(durationInterval);
                durationInterval = null;
            }
            
            if (hls) {
                hls.destroy();
                hls = null;
            }
            
            startBtn.disabled = false;
            stopBtn.disabled = true;
            testStatus.innerHTML = `Test Completed - ${refreshCount} refreshes, ${segmentCount} segments detected`;
            testStatus.className = 'status status-success';
            
            log('✅ Test stopped successfully', 'success');
            log(`📊 Final Results: ${refreshCount} refreshes, ${segmentCount} segments, ${errorCount} errors`, 'info');
        }
        
        function clearLogs() {
            testLogs.innerHTML = '<div class="log-entry log-info">Logs cleared...</div>';
        }
        
        // Update metrics initially
        updateMetrics();
    </script>
</body>
</html>
