# 🚀 Batch Compression & S3 Support Update

Your Y4M Video Streamer now supports advanced batch processing with full S3 integration!

## ✅ **New Features Added**

### 🔄 **Dual Compression Modes**

#### **1. Single File Mode (Original)**
- ✅ Process individual video files
- ✅ Works with current video loaded in player
- ✅ Outputs to local `compressed/` directory
- ✅ Real-time progress and logs

#### **2. Batch Processing Mode (NEW)**
- ✅ Process entire directories of Y4M files
- ✅ Support for both local and S3 input/output
- ✅ Configurable input and output destinations
- ✅ Batch progress monitoring

### 🌐 **S3 Integration Support**

The compression script now supports all combinations:

#### **Local to Local**
```bash
./script.sh /home/<USER>/raw /home/<USER>/encoded
```

#### **S3 to Local**
```bash
./script.sh s3://raw-videos/batch1/ /home/<USER>/encoded
```

#### **Local to S3**
```bash
./script.sh /home/<USER>/raw s3://encoded-videos/batch1/
```

#### **S3 to S3**
```bash
./script.sh s3://raw-videos/input/ s3://processed-videos/output/
```

### 🎛️ **Enhanced UI Controls**

#### **Mode Selection**
- Radio buttons to switch between Single File and Batch modes
- Clear visual separation of different compression options
- Context-appropriate controls for each mode

#### **Batch Mode Interface**
- **Input Source Field**: Local folder path or S3 URI
- **Output Destination Field**: Local folder path or S3 URI
- **Smart Validation**: Ensures both paths are provided
- **Clear Instructions**: Helper text for proper formatting

### 🔧 **Updated Compression Script**

#### **New Script Capabilities**
- ✅ **Dual Arguments**: Takes both input and output paths
- ✅ **S3 Detection**: Automatically detects S3 URIs vs local paths
- ✅ **Batch Processing**: Finds all .y4m files in input directory
- ✅ **Smart Handling**: Downloads from S3, processes, uploads results
- ✅ **File Management**: Moves originals to `original/` folder
- ✅ **Progress Reporting**: Detailed logging of each step

#### **Script Usage Examples**
```bash
# Process local directory to local output
./compress_code_video_update.sh /path/to/input /path/to/output

# Download from S3, process locally, upload to S3
./compress_code_video_update.sh s3://input-bucket/videos/ s3://output-bucket/compressed/

# Mixed scenarios supported
./compress_code_video_update.sh s3://input/ /local/output/
./compress_code_video_update.sh /local/input/ s3://output/
```

## 🎯 **How to Use**

### **Single File Compression (Existing)**
1. Load a video file (upload or select from list)
2. Ensure "Single File" mode is selected
3. Click "Run Compression"
4. Monitor progress and logs

### **Batch Compression (NEW)**
1. Select "Batch Processing" mode
2. Enter **Input Source**:
   - Local: `/path/to/folder/with/y4m/files`
   - S3: `s3://bucket-name/folder/`
3. Enter **Output Destination**:
   - Local: `/path/to/output/folder`
   - S3: `s3://output-bucket/folder/`
4. Click "Run Batch Compression"
5. Monitor batch progress and logs

### **S3 Configuration**
For S3 operations, ensure your environment has:
- AWS credentials configured (`.env` file or AWS CLI)
- Proper S3 bucket permissions
- AWS CLI installed (for script operations)

## 🔍 **Technical Details**

### **Backend Changes**
- ✅ **Compression Service**: Updated to handle both single and batch modes
- ✅ **API Endpoints**: Enhanced to accept mode and output path parameters
- ✅ **Job Management**: Tracks different compression types
- ✅ **Logging**: Mode-specific log messages and status updates

### **Frontend Changes**
- ✅ **UI Components**: New mode selection and batch input fields
- ✅ **JavaScript Logic**: Separate handlers for single vs batch compression
- ✅ **Validation**: Input validation for batch mode requirements
- ✅ **Status Display**: Clear indication of compression mode and progress

### **Script Enhancements**
- ✅ **Argument Handling**: Validates input/output arguments
- ✅ **S3 Operations**: Uses AWS CLI for S3 sync and copy operations
- ✅ **Error Handling**: Robust error checking and reporting
- ✅ **Cleanup**: Proper cleanup of temporary files and directories

## 🚀 **Ready to Test**

**Open your browser to:** http://localhost:3000

### **Test Scenarios**

1. **Single File Mode**: 
   - Upload a Y4M file and compress it (existing functionality)

2. **Local Batch Processing**:
   - Input: `uploads` (contains Y4M files)
   - Output: `compressed`

3. **S3 Batch Processing** (if AWS configured):
   - Input: `s3://your-bucket/input/`
   - Output: `s3://your-bucket/output/`

4. **Mixed Scenarios**:
   - S3 to Local: `s3://bucket/input/` → `/local/output/`
   - Local to S3: `/local/input/` → `s3://bucket/output/`

## 📋 **File Organization**

After batch processing:
- **Original Y4M files**: Moved to `original/` directory
- **Compressed MP4 files**: Created with `_zmt.mp4` suffix
- **S3 uploads**: Maintain same naming convention
- **Logs**: Available in real-time through web interface

Your Y4M Video Streamer now supports enterprise-level batch processing with full cloud integration! 🎉
