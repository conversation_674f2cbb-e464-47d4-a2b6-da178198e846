#!/usr/bin/env python3
"""
Test script to verify AWS CloudWatch logging integration
"""

import sys
import os
import time
from datetime import datetime

# Add current directory to path to import our modules
sys.path.append('.')

try:
    from hls_streaming_s3 import UploadTracker
    print("✅ Successfully imported UploadTracker with AWS integration")
except ImportError as e:
    print(f"❌ Failed to import UploadTracker: {e}")
    sys.exit(1)

def test_aws_logging():
    """Test AWS CloudWatch logging functionality"""
    
    print("🎯 Testing AWS CloudWatch Integration")
    print("=" * 50)
    
    # Test 1: Create tracker with AWS logging enabled
    print("\n📊 Test 1: Creating UploadTracker with AWS logging...")
    try:
        tracker = UploadTracker(enable_aws_logging=True, session_name="test_aws_logging")
        print("✅ UploadTracker created successfully")
    except Exception as e:
        print(f"❌ Failed to create UploadTracker: {e}")
        print("💡 Make sure AWS credentials are configured (aws configure)")
        return False
    
    # Test 2: Log some sample uploads
    print("\n📊 Test 2: Logging sample uploads...")
    sample_uploads = [
        ("test_segment_001.ts", 150000, 0.5),
        ("test_segment_002.ts", 175000, 0.6),
        ("test_segment_003.ts", 160000, 0.4),
        ("test_playlist.m3u8", 1024, 0.1)
    ]
    
    for filename, size_bytes, duration in sample_uploads:
        speed_mbps = (size_bytes / 1024 / 1024) / duration
        print(f"   Logging: {filename} ({size_bytes:,} bytes, {speed_mbps:.2f} MB/s)")
        
        try:
            tracker.log_upload(filename, size_bytes, duration, speed_mbps, "test-bucket")
            print(f"   ✅ Logged {filename} to AWS CloudWatch")
        except Exception as e:
            print(f"   ❌ Failed to log {filename}: {e}")
        
        time.sleep(0.5)  # Small delay between uploads
    
    # Test 3: Publish session summary
    print("\n📊 Test 3: Publishing session summary...")
    try:
        tracker.publish_session_summary()
        print("✅ Session summary published to AWS CloudWatch")
    except Exception as e:
        print(f"❌ Failed to publish session summary: {e}")
    
    # Test 4: Display local summary
    print("\n📊 Test 4: Local summary:")
    summary = tracker.get_summary()
    print(f"   • Total Files: {summary['total_files']}")
    print(f"   • Total Bytes: {summary['total_bytes']:,} ({summary['total_mb']:.2f} MB)")
    print(f"   • Average Speed: {summary['average_speed_mbps']:.2f} MB/s")
    print(f"   • Session Duration: {summary['session_duration_seconds']:.1f} seconds")
    
    print("\n🎉 AWS CloudWatch logging test completed!")
    print("\n💡 Check your AWS CloudWatch console:")
    print("   • Namespace: HLS/Streaming/Detailed")
    print("   • Namespace: HLS/Streaming/Sessions")
    print("   • Metrics should appear within 1-2 minutes")
    
    return True

def check_aws_credentials():
    """Check if AWS credentials are configured"""
    try:
        import boto3
        # Try to create a CloudWatch client
        cloudwatch = boto3.client('cloudwatch')
        # Try to list metrics (this will fail if credentials are not configured)
        cloudwatch.list_metrics(Namespace='AWS/S3', MaxRecords=1)
        return True
    except Exception as e:
        print(f"❌ AWS credentials not configured properly: {e}")
        print("\n💡 To configure AWS credentials:")
        print("   1. Run: aws configure")
        print("   2. Enter your AWS Access Key ID")
        print("   3. Enter your AWS Secret Access Key")
        print("   4. Enter your default region (e.g., us-west-1)")
        print("   5. Press Enter for default output format")
        return False

if __name__ == "__main__":
    print("🔧 AWS CloudWatch Logging Test")
    print("=" * 40)
    
    # Check AWS credentials first
    print("🔍 Checking AWS credentials...")
    if not check_aws_credentials():
        print("\n❌ Please configure AWS credentials before running this test")
        sys.exit(1)
    
    print("✅ AWS credentials configured")
    
    # Run the test
    success = test_aws_logging()
    
    if success:
        print("\n🎯 Next Steps:")
        print("1. Check AWS CloudWatch Console for metrics")
        print("2. Run the full HLS streaming with AWS logging enabled")
        print("3. Set up CloudWatch dashboards using setup-aws-monitoring.sh")
        sys.exit(0)
    else:
        print("\n❌ Test failed - check error messages above")
        sys.exit(1)
