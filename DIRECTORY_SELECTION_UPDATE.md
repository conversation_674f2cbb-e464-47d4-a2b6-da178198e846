# 📁 Directory Selection for Batch Processing

Your Y4M Video Streamer now includes intuitive directory selection for batch processing, making it much easier to choose input and output folders!

## ✅ **New Features Added**

### 📁 **Visual Directory Selection**

#### **Folder Picker Buttons**
- ✅ **Green Folder Icons** - Click to open directory picker
- ✅ **Input Directory Button** - Select source folder containing Y4M files
- ✅ **Output Directory Button** - Select destination folder for compressed files
- ✅ **Visual Feedback** - Buttons show checkmark when directory is selected

#### **Dual Input Methods**
- ✅ **Manual Entry** - Type or paste directory paths (existing functionality)
- ✅ **Directory Picker** - Click folder button to browse and select directories
- ✅ **S3 Support** - Manual entry still supports S3 URIs
- ✅ **Mixed Usage** - Can use picker for one path and manual entry for another

### 🔍 **Real-time Path Validation**

#### **Live Validation**
- ✅ **As-You-Type** - Paths validated while typing (debounced)
- ✅ **Visual Indicators** - Color-coded input fields show validation status
- ✅ **Icon Feedback** - Checkmarks, X's, and cloud icons for different path types
- ✅ **Error Prevention** - Invalid paths highlighted before compression starts

#### **Validation Types**
- ✅ **Local Paths** - Green border for valid local directories
- ✅ **S3 Paths** - Blue border for valid S3 URI format
- ✅ **Invalid Paths** - Red border for non-existent or invalid paths
- ✅ **Auto-Creation** - Attempts to create local output directories if they don't exist

### 🎨 **Enhanced User Interface**

#### **Improved Layout**
- ✅ **Side-by-Side Controls** - Path input field with folder button
- ✅ **Responsive Design** - Stacks vertically on mobile devices
- ✅ **Professional Styling** - Consistent with overall application design
- ✅ **Clear Labels** - Descriptive labels and helper text

#### **Visual Feedback System**
- ✅ **Button Animation** - Hover effects and click feedback
- ✅ **Success Indicators** - Temporary checkmarks when directories selected
- ✅ **Status Messages** - Clear feedback about selected directories
- ✅ **File Count Display** - Shows number of files in selected input directory

## 🔧 **Technical Implementation**

### 📂 **Directory Selection API**

#### **Browser Directory Picker**
```html
<input type="file" webkitdirectory directory multiple>
```
- Uses modern browser directory selection API
- Provides access to directory contents
- Extracts relative directory paths for server use

#### **Backend Validation Endpoints**

**Path Validation:**
```
POST /api/video/validate-path
Body: { "path": "/path/to/directory" }
```

**Directory Creation:**
```
POST /api/video/create-directory  
Body: { "path": "/path/to/new/directory" }
```

**Available Directories:**
```
GET /api/video/directories
```

### 🎯 **Path Processing Logic**

#### **Local Directory Handling**
- **Relative Paths** - Converts browser selections to relative paths
- **Absolute Paths** - Supports full system paths
- **Path Resolution** - Resolves paths relative to server working directory
- **Directory Creation** - Auto-creates output directories if needed

#### **S3 Path Support**
- **URI Validation** - Validates S3 URI format
- **Mixed Operations** - Supports local-to-S3, S3-to-local, etc.
- **Bucket Access** - Relies on AWS credentials for actual S3 operations
- **Format Checking** - Ensures proper s3:// prefix format

### 🎨 **UI Components**

#### **Path Input Container**
```css
.path-input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}
```

#### **Directory Selection Button**
```css
.dir-select-btn {
  background: #28a745;
  width: 44px;
  height: 44px;
  border-radius: 6px;
}
```

#### **Validation Styles**
- **Valid Path** - Green border and background tint
- **Invalid Path** - Red border and background tint  
- **S3 Path** - Blue border and background tint
- **Validation Icons** - Checkmark, X, or cloud icon overlay

## 🚀 **User Workflow**

### 📁 **Directory Selection Process**

#### **Method 1: Directory Picker**
1. **Click Folder Button** - Green folder icon next to input field
2. **Browse Directories** - System directory picker opens
3. **Select Folder** - Choose input or output directory
4. **Auto-Population** - Path automatically fills in input field
5. **Visual Confirmation** - Button shows checkmark, status message appears

#### **Method 2: Manual Entry**
1. **Type Path** - Enter local path or S3 URI manually
2. **Real-time Validation** - Path validated as you type
3. **Visual Feedback** - Input field color changes based on validity
4. **Error Prevention** - Invalid paths highlighted before submission

### ⚙️ **Batch Compression Workflow**
1. **Select Input** - Use picker or manual entry for source directory
2. **Select Output** - Use picker or manual entry for destination
3. **Path Validation** - Both paths validated automatically
4. **Directory Creation** - Output directory created if needed
5. **Start Compression** - Batch processing begins with validated paths

## 🎯 **Benefits**

### 👥 **User Experience**
- ✅ **Easier Selection** - No need to remember or type long paths
- ✅ **Error Reduction** - Visual validation prevents invalid paths
- ✅ **Faster Workflow** - Quick directory browsing and selection
- ✅ **Clear Feedback** - Always know if paths are valid

### 🔧 **Technical Advantages**
- ✅ **Path Validation** - Prevents compression failures due to bad paths
- ✅ **Auto-Creation** - Creates output directories automatically
- ✅ **Mixed Support** - Handles local and S3 paths seamlessly
- ✅ **Error Prevention** - Validates before starting expensive operations

### 📱 **Cross-Platform**
- ✅ **Modern Browsers** - Works in Chrome, Firefox, Safari, Edge
- ✅ **Mobile Support** - Directory selection works on mobile devices
- ✅ **Responsive Design** - Adapts to different screen sizes
- ✅ **Accessibility** - Keyboard navigation and screen reader support

## 🚀 **Ready to Test**

**Open your browser to:** http://localhost:3000

### **Test Directory Selection:**

1. **Input Directory Selection:**
   - Click the green folder button next to "Input Source"
   - Browse and select a directory containing video files
   - Watch the path auto-populate and validate

2. **Output Directory Selection:**
   - Click the green folder button next to "Output Destination"  
   - Browse and select or create an output directory
   - See the path validation and creation feedback

3. **Manual Path Entry:**
   - Type a local path like `./uploads` or `./compressed`
   - Watch real-time validation with color changes
   - Try an S3 URI like `s3://bucket/folder/`

4. **Mixed Usage:**
   - Use directory picker for input, manual entry for output
   - Combine local and S3 paths in different combinations
   - Test validation and error handling

Your batch processing workflow is now much more user-friendly with intuitive directory selection! 📁✨
