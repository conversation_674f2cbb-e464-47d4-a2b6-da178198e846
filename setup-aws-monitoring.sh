#!/bin/bash

# AWS S3 Bandwidth and Cost Monitoring Setup Script
# This script configures comprehensive AWS monitoring for S3 bandwidth and billing

set -e

echo "🎯 Setting up AWS S3 Bandwidth and Cost Monitoring"
echo "=================================================="

# Configuration variables
REGION="us-west-1"
SOURCE_BUCKET="zmt-source-video"
COMPRESSED_BUCKET="zmt-compressed-video"
LOGS_BUCKET="zmt-s3-access-logs"
CLOUDTRAIL_BUCKET="zmt-cloudtrail-logs"
COST_REPORTS_BUCKET="zmt-cost-reports"
ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

echo "📋 Configuration:"
echo "   Region: $REGION"
echo "   Account ID: $ACCOUNT_ID"
echo "   Source Bucket: $SOURCE_BUCKET"
echo "   Compressed Bucket: $COMPRESSED_BUCKET"

# Step 1: Create S3 Access Logging
echo ""
echo "📊 Step 1: Setting up S3 Server Access Logging"
echo "=============================================="

# Create logging bucket if it doesn't exist
if ! aws s3 ls "s3://$LOGS_BUCKET" 2>/dev/null; then
    echo "Creating S3 access logs bucket: $LOGS_BUCKET"
    aws s3 mb "s3://$LOGS_BUCKET" --region $REGION
else
    echo "S3 access logs bucket already exists: $LOGS_BUCKET"
fi

# Set bucket policy for S3 logging
echo "Setting bucket policy for S3 access logging..."
cat > /tmp/s3-logging-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "S3ServerAccessLogsPolicy",
      "Effect": "Allow",
      "Principal": {
        "Service": "logging.s3.amazonaws.com"
      },
      "Action": "s3:PutObject",
      "Resource": "arn:aws:s3:::$LOGS_BUCKET/*",
      "Condition": {
        "ArnLike": {
          "aws:SourceArn": [
            "arn:aws:s3:::$SOURCE_BUCKET",
            "arn:aws:s3:::$COMPRESSED_BUCKET"
          ]
        }
      }
    },
    {
      "Sid": "S3ServerAccessLogsDelivery",
      "Effect": "Allow",
      "Principal": {
        "Service": "logging.s3.amazonaws.com"
      },
      "Action": "s3:GetBucketAcl",
      "Resource": "arn:aws:s3:::$LOGS_BUCKET"
    }
  ]
}
EOF

aws s3api put-bucket-policy --bucket $LOGS_BUCKET --policy file:///tmp/s3-logging-policy.json

# Enable logging on target buckets
echo "Enabling access logging for $SOURCE_BUCKET..."
aws s3api put-bucket-logging --bucket $SOURCE_BUCKET --bucket-logging-status "{
  \"LoggingEnabled\": {
    \"TargetBucket\": \"$LOGS_BUCKET\",
    \"TargetPrefix\": \"source-video-logs/\"
  }
}"

echo "Enabling access logging for $COMPRESSED_BUCKET..."
aws s3api put-bucket-logging --bucket $COMPRESSED_BUCKET --bucket-logging-status "{
  \"LoggingEnabled\": {
    \"TargetBucket\": \"$LOGS_BUCKET\",
    \"TargetPrefix\": \"compressed-video-logs/\"
  }
}"

# Step 2: Setup CloudTrail Data Events
echo ""
echo "🔍 Step 2: Setting up CloudTrail Data Events"
echo "==========================================="

# Create CloudTrail bucket if it doesn't exist
if ! aws s3 ls "s3://$CLOUDTRAIL_BUCKET" 2>/dev/null; then
    echo "Creating CloudTrail logs bucket: $CLOUDTRAIL_BUCKET"
    aws s3 mb "s3://$CLOUDTRAIL_BUCKET" --region $REGION
else
    echo "CloudTrail logs bucket already exists: $CLOUDTRAIL_BUCKET"
fi

# Set CloudTrail bucket policy
echo "Setting CloudTrail bucket policy..."
cat > /tmp/cloudtrail-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AWSCloudTrailAclCheck",
      "Effect": "Allow",
      "Principal": {
        "Service": "cloudtrail.amazonaws.com"
      },
      "Action": "s3:GetBucketAcl",
      "Resource": "arn:aws:s3:::$CLOUDTRAIL_BUCKET"
    },
    {
      "Sid": "AWSCloudTrailWrite",
      "Effect": "Allow",
      "Principal": {
        "Service": "cloudtrail.amazonaws.com"
      },
      "Action": "s3:PutObject",
      "Resource": "arn:aws:s3:::$CLOUDTRAIL_BUCKET/*",
      "Condition": {
        "StringEquals": {
          "s3:x-amz-acl": "bucket-owner-full-control"
        }
      }
    }
  ]
}
EOF

aws s3api put-bucket-policy --bucket $CLOUDTRAIL_BUCKET --policy file:///tmp/cloudtrail-policy.json

# Create or update CloudTrail
echo "Creating CloudTrail for S3 data events..."
if aws cloudtrail describe-trails --trail-name-list zmt-s3-data-events 2>/dev/null | grep -q "zmt-s3-data-events"; then
    echo "CloudTrail already exists, updating..."
    aws cloudtrail update-trail \
        --name zmt-s3-data-events \
        --s3-bucket-name $CLOUDTRAIL_BUCKET
else
    echo "Creating new CloudTrail..."
    aws cloudtrail create-trail \
        --name zmt-s3-data-events \
        --s3-bucket-name $CLOUDTRAIL_BUCKET \
        --include-global-service-events \
        --is-multi-region-trail \
        --enable-log-file-validation
fi

# Configure data events
echo "Configuring S3 data events..."
aws cloudtrail put-event-selectors \
    --trail-name zmt-s3-data-events \
    --event-selectors "[
      {
        \"ReadWriteType\": \"All\",
        \"IncludeManagementEvents\": true,
        \"DataResources\": [
          {
            \"Type\": \"AWS::S3::Object\",
            \"Values\": [
              \"arn:aws:s3:::$SOURCE_BUCKET/*\",
              \"arn:aws:s3:::$COMPRESSED_BUCKET/*\"
            ]
          }
        ]
      }
    ]"

# Start logging
aws cloudtrail start-logging --name zmt-s3-data-events

# Step 3: Enable S3 Request Metrics
echo ""
echo "📈 Step 3: Enabling S3 Request Metrics"
echo "====================================="

echo "Enabling request metrics for $SOURCE_BUCKET..."
aws s3api put-bucket-metrics-configuration \
    --bucket $SOURCE_BUCKET \
    --id EntireBucket \
    --metrics-configuration '{
        "Id": "EntireBucket",
        "Filter": {
            "Prefix": ""
        }
    }'

echo "Enabling request metrics for $COMPRESSED_BUCKET..."
aws s3api put-bucket-metrics-configuration \
    --bucket $COMPRESSED_BUCKET \
    --id EntireBucket \
    --metrics-configuration '{
        "Id": "EntireBucket",
        "Filter": {
            "Prefix": ""
        }
    }'

# Step 4: Setup Cost and Usage Reports
echo ""
echo "💰 Step 4: Setting up Cost and Usage Reports"
echo "==========================================="

# Create cost reports bucket in us-east-1 (required for billing)
if ! aws s3 ls "s3://$COST_REPORTS_BUCKET" --region us-east-1 2>/dev/null; then
    echo "Creating cost reports bucket: $COST_REPORTS_BUCKET"
    aws s3 mb "s3://$COST_REPORTS_BUCKET" --region us-east-1
else
    echo "Cost reports bucket already exists: $COST_REPORTS_BUCKET"
fi

# Set cost reports bucket policy
echo "Setting cost reports bucket policy..."
cat > /tmp/cost-reports-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "billingreports.amazonaws.com"
      },
      "Action": [
        "s3:GetBucketAcl",
        "s3:GetBucketPolicy"
      ],
      "Resource": "arn:aws:s3:::$COST_REPORTS_BUCKET"
    },
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "billingreports.amazonaws.com"
      },
      "Action": "s3:PutObject",
      "Resource": "arn:aws:s3:::$COST_REPORTS_BUCKET/*"
    }
  ]
}
EOF

aws s3api put-bucket-policy --bucket $COST_REPORTS_BUCKET --policy file:///tmp/cost-reports-policy.json --region us-east-1

# Step 5: Create CloudWatch Dashboard
echo ""
echo "📊 Step 5: Creating CloudWatch Dashboard"
echo "======================================="

echo "Creating S3 Bandwidth Monitoring Dashboard..."
python3 -c "
import boto3
import json

cloudwatch = boto3.client('cloudwatch', region_name='$REGION')

dashboard_body = {
    'widgets': [
        {
            'type': 'metric',
            'x': 0, 'y': 0, 'width': 12, 'height': 6,
            'properties': {
                'metrics': [
                    ['AWS/S3', 'BucketSizeBytes', 'BucketName', '$COMPRESSED_BUCKET', 'StorageType', 'StandardStorage'],
                    ['.', '.', '.', '$SOURCE_BUCKET', '.', '.']
                ],
                'period': 86400,
                'stat': 'Average',
                'region': '$REGION',
                'title': 'S3 Bucket Size',
                'yAxis': {'left': {'min': 0}}
            }
        },
        {
            'type': 'metric',
            'x': 12, 'y': 0, 'width': 12, 'height': 6,
            'properties': {
                'metrics': [
                    ['AWS/S3', 'AllRequests', 'BucketName', '$COMPRESSED_BUCKET'],
                    ['.', 'BytesDownloaded', '.', '.'],
                    ['.', 'BytesUploaded', '.', '.']
                ],
                'period': 300,
                'stat': 'Sum',
                'region': '$REGION',
                'title': 'S3 Requests and Data Transfer',
                'yAxis': {'left': {'min': 0}}
            }
        },
        {
            'type': 'metric',
            'x': 0, 'y': 6, 'width': 24, 'height': 6,
            'properties': {
                'metrics': [
                    ['HLS/Streaming/Detailed', 'BytesUploaded', 'BucketName', '$COMPRESSED_BUCKET'],
                    ['.', 'UploadSpeed', '.', '.']
                ],
                'period': 60,
                'stat': 'Average',
                'region': '$REGION',
                'title': 'HLS Streaming Metrics',
                'yAxis': {'left': {'min': 0}}
            }
        }
    ]
}

cloudwatch.put_dashboard(
    DashboardName='S3-Bandwidth-Monitoring',
    DashboardBody=json.dumps(dashboard_body)
)

print('✅ Created CloudWatch Dashboard: S3-Bandwidth-Monitoring')
"

# Step 6: Verification
echo ""
echo "✅ Step 6: Verification"
echo "====================="

echo "Verifying S3 access logging configuration..."
aws s3api get-bucket-logging --bucket $SOURCE_BUCKET
aws s3api get-bucket-logging --bucket $COMPRESSED_BUCKET

echo ""
echo "Verifying CloudTrail status..."
aws cloudtrail get-trail-status --name zmt-s3-data-events

echo ""
echo "Verifying S3 request metrics..."
aws s3api list-bucket-metrics-configurations --bucket $SOURCE_BUCKET
aws s3api list-bucket-metrics-configurations --bucket $COMPRESSED_BUCKET

# Cleanup temporary files
rm -f /tmp/s3-logging-policy.json
rm -f /tmp/cloudtrail-policy.json
rm -f /tmp/cost-reports-policy.json

echo ""
echo "🎉 AWS S3 Bandwidth and Cost Monitoring Setup Complete!"
echo "======================================================="
echo ""
echo "📋 Summary of configured components:"
echo "   ✅ S3 Server Access Logging"
echo "   ✅ CloudTrail Data Events for S3"
echo "   ✅ S3 Request Metrics"
echo "   ✅ Cost and Usage Reports bucket"
echo "   ✅ CloudWatch Dashboard"
echo ""
echo "🔗 Next steps:"
echo "   1. Wait 15-30 minutes for metrics to start appearing"
echo "   2. Check CloudWatch Dashboard: S3-Bandwidth-Monitoring"
echo "   3. Run the comprehensive monitoring script with your HLS uploads"
echo "   4. Monitor costs in AWS Cost Explorer"
echo ""
echo "📊 Access your monitoring:"
echo "   • CloudWatch Dashboard: https://console.aws.amazon.com/cloudwatch/home?region=$REGION#dashboards:name=S3-Bandwidth-Monitoring"
echo "   • Cost Explorer: https://console.aws.amazon.com/cost-management/home#/cost-explorer"
echo "   • S3 Access Logs: s3://$LOGS_BUCKET/"
echo "   • CloudTrail Logs: s3://$CLOUDTRAIL_BUCKET/"
