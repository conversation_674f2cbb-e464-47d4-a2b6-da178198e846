const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

class CompressionService {
  constructor() {
    this.activeJobs = new Map();
    this.jobLogs = new Map();
  }

  /**
   * Start compression process
   * @param {string} inputPath - Input file path or S3 URI
   * @param {object} io - Socket.io instance
   * @param {boolean} isS3 - Whether input is from S3
   * @param {string} mode - Compression mode ('single' or 'batch')
   * @param {string} outputPath - Output path for batch mode
   * @returns {Promise<string>} - Compression job ID
   */
  async startCompression(inputPath, io, isS3 = false, mode = 'single', outputPath = null) {
    const jobId = crypto.randomUUID();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

    // Handle different modes
    let scriptArgs = [];
    let jobOutputPath = '';

    if (mode === 'batch') {
      // Batch mode: script takes input and output paths
      scriptArgs = [inputPath, outputPath];
      jobOutputPath = outputPath;
    } else {
      // Single file mode: For the new script, we need to create a temp directory with the single file
      // and use the batch script format
      const outputDir = 'compressed';

      // Ensure output directory exists
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      // For single file, we'll use the directory containing the file as input
      // and compressed as output - the script will find the Y4M file
      const inputDir = path.dirname(inputPath);
      scriptArgs = [inputDir, outputDir];

      const inputBasename = path.basename(inputPath, path.extname(inputPath));
      jobOutputPath = path.join(outputDir, `${inputBasename}_zmt.mp4`);
    }

    // Initialize job status
    const jobStatus = {
      id: jobId,
      inputPath: inputPath,
      outputPath: jobOutputPath,
      isS3: isS3,
      mode: mode,
      status: 'starting',
      progress: 0,
      startTime: new Date(),
      endTime: null,
      error: null,
      pid: null,
      inputFiles: [],
      outputFiles: []
    };

    this.activeJobs.set(jobId, jobStatus);
    this.jobLogs.set(jobId, []);

    // Emit initial status
    io.emit('compressionStatus', jobStatus);

    try {
      // Check if compression script exists
      const scriptPath = './compress_code_video_update.sh';
      if (!fs.existsSync(scriptPath)) {
        throw new Error('Compression script not found: compress_code_video_update.sh');
      }

      // Make script executable
      fs.chmodSync(scriptPath, '755');

      // Detect input files and emit to frontend
      await this.detectAndEmitInputFiles(jobId, inputPath, mode, io);

      // Start monitoring output directory for new files
      this.startOutputMonitoring(jobId, jobOutputPath, mode, io);

      // Start compression process
      const child = spawn('bash', [scriptPath, ...scriptArgs], {
        cwd: process.cwd(),
        stdio: ['pipe', 'pipe', 'pipe']
      });

      jobStatus.pid = child.pid;
      jobStatus.status = 'running';
      this.activeJobs.set(jobId, jobStatus);

      // Log initial message
      const modeText = mode === 'batch' ? 'batch compression' : 'compression';
      this.addLog(jobId, `Starting ${modeText}: ${inputPath} -> ${jobOutputPath}`);
      this.addLog(jobId, `Process PID: ${child.pid}`);

      // Handle stdout
      child.stdout.on('data', (data) => {
        const output = data.toString();
        this.addLog(jobId, `STDOUT: ${output}`);
        
        // Try to parse progress from output (if your script provides it)
        const progressMatch = output.match(/(\d+)%/);
        if (progressMatch) {
          jobStatus.progress = parseInt(progressMatch[1]);
          this.activeJobs.set(jobId, jobStatus);
          io.emit('compressionProgress', { jobId, progress: jobStatus.progress });
        }
      });

      // Handle stderr
      child.stderr.on('data', (data) => {
        const error = data.toString();
        this.addLog(jobId, `STDERR: ${error}`);
      });

      // Handle process completion
      child.on('close', (code) => {
        jobStatus.endTime = new Date();
        
        if (code === 0) {
          jobStatus.status = 'completed';
          jobStatus.progress = 100;
          const modeText = mode === 'batch' ? 'Batch compression' : 'Compression';
          this.addLog(jobId, `${modeText} completed successfully`);

          // Check if output exists (for single file mode)
          if (mode === 'single' && fs.existsSync(jobOutputPath)) {
            const stats = fs.statSync(jobOutputPath);
            jobStatus.outputSize = stats.size;
            this.addLog(jobId, `Output file size: ${stats.size} bytes`);
          } else if (mode === 'batch') {
            this.addLog(jobId, `Batch processing completed. Check output destination: ${jobOutputPath}`);
          }
        } else {
          jobStatus.status = 'failed';
          jobStatus.error = `Process exited with code ${code}`;
          const modeText = mode === 'batch' ? 'Batch compression' : 'Compression';
          this.addLog(jobId, `${modeText} failed with exit code: ${code}`);
        }

        this.activeJobs.set(jobId, jobStatus);

        // Clean up watchers
        this.cleanupJob(jobId);

        io.emit('compressionComplete', jobStatus);
      });

      // Handle process error
      child.on('error', (error) => {
        jobStatus.status = 'failed';
        jobStatus.error = error.message;
        jobStatus.endTime = new Date();
        this.addLog(jobId, `Process error: ${error.message}`);
        this.activeJobs.set(jobId, jobStatus);
        io.emit('compressionError', { jobId, error: error.message });
      });

      // Emit status update
      io.emit('compressionStatus', jobStatus);

    } catch (error) {
      jobStatus.status = 'failed';
      jobStatus.error = error.message;
      jobStatus.endTime = new Date();
      this.addLog(jobId, `Setup error: ${error.message}`);
      this.activeJobs.set(jobId, jobStatus);
      io.emit('compressionError', { jobId, error: error.message });
    }

    return jobId;
  }

  /**
   * Get compression job status
   * @param {string} jobId - Job ID
   * @returns {object|null} - Job status
   */
  getCompressionStatus(jobId) {
    return this.activeJobs.get(jobId) || null;
  }

  /**
   * Cancel compression job
   * @param {string} jobId - Job ID
   * @returns {boolean} - Success status
   */
  cancelCompression(jobId) {
    const job = this.activeJobs.get(jobId);
    if (!job || !job.pid) {
      return false;
    }

    try {
      process.kill(job.pid, 'SIGTERM');
      job.status = 'cancelled';
      job.endTime = new Date();
      this.addLog(jobId, 'Compression cancelled by user');
      this.activeJobs.set(jobId, job);
      return true;
    } catch (error) {
      console.error('Failed to cancel compression:', error);
      return false;
    }
  }

  /**
   * Get compression logs
   * @param {string} jobId - Job ID
   * @returns {Array} - Log entries
   */
  getCompressionLogs(jobId) {
    return this.jobLogs.get(jobId) || [];
  }

  /**
   * Add log entry
   * @param {string} jobId - Job ID
   * @param {string} message - Log message
   */
  addLog(jobId, message) {
    const logs = this.jobLogs.get(jobId) || [];
    logs.push({
      timestamp: new Date().toISOString(),
      message: message
    });
    this.jobLogs.set(jobId, logs);
    console.log(`[${jobId}] ${message}`);
  }

  /**
   * Clean up old jobs (call periodically)
   * @param {number} maxAge - Maximum age in milliseconds (default: 24 hours)
   */
  cleanupOldJobs(maxAge = 24 * 60 * 60 * 1000) {
    const now = new Date();
    for (const [jobId, job] of this.activeJobs.entries()) {
      if (job.endTime && (now - job.endTime) > maxAge) {
        this.activeJobs.delete(jobId);
        this.jobLogs.delete(jobId);
      }
    }
  }

  /**
   * Get all active jobs
   * @returns {Array} - List of active jobs
   */
  getAllJobs() {
    return Array.from(this.activeJobs.values());
  }

  /**
   * Detect input files and emit to frontend
   * @param {string} jobId - Job ID
   * @param {string} inputPath - Input path
   * @param {string} mode - Compression mode
   * @param {object} io - Socket.io instance
   */
  async detectAndEmitInputFiles(jobId, inputPath, mode, io) {
    try {
      let inputFiles = [];

      if (mode === 'single') {
        // For single file mode, the input is the file itself
        if (fs.existsSync(inputPath)) {
          const stats = fs.statSync(inputPath);
          if (stats.isFile()) {
            inputFiles.push({
              filename: path.basename(inputPath),
              path: inputPath,
              size: stats.size,
              url: this.getFileUrl(inputPath)
            });
          }
        }
      } else {
        // For batch mode, scan directory for Y4M files
        if (!inputPath.startsWith('s3://') && fs.existsSync(inputPath)) {
          const files = fs.readdirSync(inputPath);
          inputFiles = files
            .filter(file => file.toLowerCase().endsWith('.y4m'))
            .map(file => {
              const filePath = path.join(inputPath, file);
              const stats = fs.statSync(filePath);
              return {
                filename: file,
                path: filePath,
                size: stats.size,
                url: this.getFileUrl(filePath)
              };
            });
        }
      }

      // Update job status
      const job = this.activeJobs.get(jobId);
      if (job) {
        job.inputFiles = inputFiles;
        this.activeJobs.set(jobId, job);
      }

      // Emit input files to frontend
      io.emit('inputFilesDetected', {
        jobId: jobId,
        inputFiles: inputFiles,
        mode: mode
      });

      this.addLog(jobId, `Detected ${inputFiles.length} input file(s)`);
    } catch (error) {
      this.addLog(jobId, `Error detecting input files: ${error.message}`);
    }
  }

  /**
   * Start monitoring output directory for new files
   * @param {string} jobId - Job ID
   * @param {string} outputPath - Output path
   * @param {string} mode - Compression mode
   * @param {object} io - Socket.io instance
   */
  startOutputMonitoring(jobId, outputPath, mode, io) {
    // Don't monitor S3 paths or if output path doesn't exist yet
    if (outputPath.startsWith('s3://')) {
      this.addLog(jobId, 'S3 output monitoring not implemented');
      return;
    }

    // Ensure output directory exists
    if (!fs.existsSync(outputPath)) {
      if (mode === 'single') {
        // For single mode, outputPath is a file, so get the directory
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
          fs.mkdirSync(outputDir, { recursive: true });
        }
      } else {
        // For batch mode, outputPath is a directory
        fs.mkdirSync(outputPath, { recursive: true });
      }
    }

    const watchPath = mode === 'single' ? path.dirname(outputPath) : outputPath;

    this.addLog(jobId, `Starting output monitoring: ${watchPath}`);

    // Watch for file changes in output directory
    const watcher = fs.watch(watchPath, (eventType, filename) => {
      if (eventType === 'rename' && filename) {
        const filePath = path.join(watchPath, filename);

        // Check if it's a video file and it exists (not deleted)
        if (fs.existsSync(filePath) && this.isVideoFile(filename)) {
          const stats = fs.statSync(filePath);

          // Wait a moment for file to be fully written
          setTimeout(() => {
            if (fs.existsSync(filePath)) {
              const outputFile = {
                filename: filename,
                path: filePath,
                size: stats.size,
                url: this.getFileUrl(filePath)
              };

              // Update job status
              const job = this.activeJobs.get(jobId);
              if (job) {
                job.outputFiles.push(outputFile);
                this.activeJobs.set(jobId, job);
              }

              // Emit new output file to frontend
              io.emit('outputFileCreated', {
                jobId: jobId,
                outputFile: outputFile,
                mode: mode
              });

              this.addLog(jobId, `New output file detected: ${filename}`);
            }
          }, 1000); // Wait 1 second for file to be fully written
        }
      }
    });

    // Store watcher reference for cleanup
    const job = this.activeJobs.get(jobId);
    if (job) {
      job.outputWatcher = watcher;
      this.activeJobs.set(jobId, job);
    }
  }

  /**
   * Get file URL for serving
   * @param {string} filePath - File path
   * @returns {string} - URL for serving the file
   */
  getFileUrl(filePath) {
    const relativePath = path.relative(process.cwd(), filePath);

    if (relativePath.startsWith('uploads')) {
      return `/${relativePath}`;
    } else if (relativePath.startsWith('compressed')) {
      return `/${relativePath}`;
    } else {
      // For other paths, try to serve from uploads or compressed
      const filename = path.basename(filePath);
      return `/videos/${filename}`;
    }
  }

  /**
   * Check if file is a video file
   * @param {string} filename - Filename
   * @returns {boolean} - Whether file is a video
   */
  isVideoFile(filename) {
    const videoExtensions = ['.y4m', '.mp4', '.avi', '.mov', '.mkv', '.webm'];
    const ext = path.extname(filename).toLowerCase();
    return videoExtensions.includes(ext);
  }

  /**
   * Clean up job resources
   * @param {string} jobId - Job ID
   */
  cleanupJob(jobId) {
    const job = this.activeJobs.get(jobId);
    if (job && job.outputWatcher) {
      job.outputWatcher.close();
      this.addLog(jobId, 'Output monitoring stopped');
    }
  }
}

module.exports = new CompressionService();
