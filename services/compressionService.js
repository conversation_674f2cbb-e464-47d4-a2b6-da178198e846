const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

class CompressionService {
  constructor() {
    this.activeJobs = new Map();
    this.jobLogs = new Map();
  }

  /**
   * Start compression process
   * @param {string} inputPath - Input file path or S3 URI
   * @param {object} io - Socket.io instance
   * @param {boolean} isS3 - Whether input is from S3
   * @param {string} mode - Compression mode ('single' or 'batch')
   * @param {string} outputPath - Output path for batch mode
   * @returns {Promise<string>} - Compression job ID
   */
  async startCompression(inputPath, io, isS3 = false, mode = 'single', outputPath = null) {
    const jobId = crypto.randomUUID();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

    // Handle different modes
    let scriptArgs = [];
    let jobOutputPath = '';

    if (mode === 'batch') {
      // Batch mode: script takes input and output paths
      scriptArgs = [inputPath, outputPath];
      jobOutputPath = outputPath;
    } else {
      // Single file mode: script takes input path, outputs to compressed/
      const outputDir = 'compressed';

      // Ensure output directory exists
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      const inputBasename = path.basename(inputPath, path.extname(inputPath));
      jobOutputPath = path.join(outputDir, `${inputBasename}-compressed-${timestamp}.mp4`);
      scriptArgs = [inputPath];
    }

    // Initialize job status
    const jobStatus = {
      id: jobId,
      inputPath: inputPath,
      outputPath: jobOutputPath,
      isS3: isS3,
      mode: mode,
      status: 'starting',
      progress: 0,
      startTime: new Date(),
      endTime: null,
      error: null,
      pid: null
    };

    this.activeJobs.set(jobId, jobStatus);
    this.jobLogs.set(jobId, []);

    // Emit initial status
    io.emit('compressionStatus', jobStatus);

    try {
      // Check if compression script exists
      const scriptPath = './compress_code_video_update.sh';
      if (!fs.existsSync(scriptPath)) {
        throw new Error('Compression script not found: compress_code_video_update.sh');
      }

      // Make script executable
      fs.chmodSync(scriptPath, '755');

      // Start compression process
      const child = spawn('bash', [scriptPath, ...scriptArgs], {
        cwd: process.cwd(),
        stdio: ['pipe', 'pipe', 'pipe']
      });

      jobStatus.pid = child.pid;
      jobStatus.status = 'running';
      this.activeJobs.set(jobId, jobStatus);

      // Log initial message
      const modeText = mode === 'batch' ? 'batch compression' : 'compression';
      this.addLog(jobId, `Starting ${modeText}: ${inputPath} -> ${jobOutputPath}`);
      this.addLog(jobId, `Process PID: ${child.pid}`);

      // Handle stdout
      child.stdout.on('data', (data) => {
        const output = data.toString();
        this.addLog(jobId, `STDOUT: ${output}`);
        
        // Try to parse progress from output (if your script provides it)
        const progressMatch = output.match(/(\d+)%/);
        if (progressMatch) {
          jobStatus.progress = parseInt(progressMatch[1]);
          this.activeJobs.set(jobId, jobStatus);
          io.emit('compressionProgress', { jobId, progress: jobStatus.progress });
        }
      });

      // Handle stderr
      child.stderr.on('data', (data) => {
        const error = data.toString();
        this.addLog(jobId, `STDERR: ${error}`);
      });

      // Handle process completion
      child.on('close', (code) => {
        jobStatus.endTime = new Date();
        
        if (code === 0) {
          jobStatus.status = 'completed';
          jobStatus.progress = 100;
          const modeText = mode === 'batch' ? 'Batch compression' : 'Compression';
          this.addLog(jobId, `${modeText} completed successfully`);

          // Check if output exists (for single file mode)
          if (mode === 'single' && fs.existsSync(jobOutputPath)) {
            const stats = fs.statSync(jobOutputPath);
            jobStatus.outputSize = stats.size;
            this.addLog(jobId, `Output file size: ${stats.size} bytes`);
          } else if (mode === 'batch') {
            this.addLog(jobId, `Batch processing completed. Check output destination: ${jobOutputPath}`);
          }
        } else {
          jobStatus.status = 'failed';
          jobStatus.error = `Process exited with code ${code}`;
          const modeText = mode === 'batch' ? 'Batch compression' : 'Compression';
          this.addLog(jobId, `${modeText} failed with exit code: ${code}`);
        }

        this.activeJobs.set(jobId, jobStatus);
        io.emit('compressionComplete', jobStatus);
      });

      // Handle process error
      child.on('error', (error) => {
        jobStatus.status = 'failed';
        jobStatus.error = error.message;
        jobStatus.endTime = new Date();
        this.addLog(jobId, `Process error: ${error.message}`);
        this.activeJobs.set(jobId, jobStatus);
        io.emit('compressionError', { jobId, error: error.message });
      });

      // Emit status update
      io.emit('compressionStatus', jobStatus);

    } catch (error) {
      jobStatus.status = 'failed';
      jobStatus.error = error.message;
      jobStatus.endTime = new Date();
      this.addLog(jobId, `Setup error: ${error.message}`);
      this.activeJobs.set(jobId, jobStatus);
      io.emit('compressionError', { jobId, error: error.message });
    }

    return jobId;
  }

  /**
   * Get compression job status
   * @param {string} jobId - Job ID
   * @returns {object|null} - Job status
   */
  getCompressionStatus(jobId) {
    return this.activeJobs.get(jobId) || null;
  }

  /**
   * Cancel compression job
   * @param {string} jobId - Job ID
   * @returns {boolean} - Success status
   */
  cancelCompression(jobId) {
    const job = this.activeJobs.get(jobId);
    if (!job || !job.pid) {
      return false;
    }

    try {
      process.kill(job.pid, 'SIGTERM');
      job.status = 'cancelled';
      job.endTime = new Date();
      this.addLog(jobId, 'Compression cancelled by user');
      this.activeJobs.set(jobId, job);
      return true;
    } catch (error) {
      console.error('Failed to cancel compression:', error);
      return false;
    }
  }

  /**
   * Get compression logs
   * @param {string} jobId - Job ID
   * @returns {Array} - Log entries
   */
  getCompressionLogs(jobId) {
    return this.jobLogs.get(jobId) || [];
  }

  /**
   * Add log entry
   * @param {string} jobId - Job ID
   * @param {string} message - Log message
   */
  addLog(jobId, message) {
    const logs = this.jobLogs.get(jobId) || [];
    logs.push({
      timestamp: new Date().toISOString(),
      message: message
    });
    this.jobLogs.set(jobId, logs);
    console.log(`[${jobId}] ${message}`);
  }

  /**
   * Clean up old jobs (call periodically)
   * @param {number} maxAge - Maximum age in milliseconds (default: 24 hours)
   */
  cleanupOldJobs(maxAge = 24 * 60 * 60 * 1000) {
    const now = new Date();
    for (const [jobId, job] of this.activeJobs.entries()) {
      if (job.endTime && (now - job.endTime) > maxAge) {
        this.activeJobs.delete(jobId);
        this.jobLogs.delete(jobId);
      }
    }
  }

  /**
   * Get all active jobs
   * @returns {Array} - List of active jobs
   */
  getAllJobs() {
    return Array.from(this.activeJobs.values());
  }
}

module.exports = new CompressionService();
