const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

class CompressionService {
  constructor() {
    this.activeJobs = new Map();
    this.jobLogs = new Map();
  }

  /**
   * Start a compression job
   * @param {string} inputPath - Input path
   * @param {string} outputPath - Output path
   * @param {string} mode - Compression mode (batch only)
   * @param {string} inputRegion - AWS region for input S3 bucket
   * @param {string} outputRegion - AWS region for output S3 bucket
   * @param {object} io - Socket.io instance
   * @returns {string} - Job ID
   */
  async startCompression(inputPath, outputPath, mode = 'batch', inputRegion = null, outputRegion = null, io) {
    const jobId = crypto.randomUUID();
    const jobOutputPath = outputPath || path.join(process.cwd(), 'compressed');
    
    // Create job status object
    const jobStatus = {
      id: jobId,
      inputPath,
      outputPath: jobOutputPath,
      mode,
      status: 'starting',
      progress: 0,
      logs: [],
      startTime: new Date(),
      pid: null
    };
    
    // Add job to active jobs
    this.activeJobs.set(jobId, jobStatus);
    
    // Build command arguments
    const args = [inputPath, jobOutputPath];
    
    // Add region arguments if provided
    if (inputRegion) {
      args.push(inputRegion);
      if (outputRegion && outputRegion !== inputRegion) {
        args.push(outputRegion);
      }
    }
    
    try {
      // Use batch compression script
      const scriptPath = './compress_code_video_hls.sh';

      if (!fs.existsSync(scriptPath)) {
        throw new Error(`Script not found: ${scriptPath}`);
      }

      // Make script executable
      fs.chmodSync(scriptPath, '755');

      // Detect input files and emit to frontend
      await this.detectAndEmitInputFiles(jobId, inputPath, mode, io);

      // Start monitoring output directory for new files
      this.startOutputMonitoring(jobId, jobOutputPath, mode, io);

      this.addLog(jobId, `Starting compression: ${inputPath} -> ${jobOutputPath}`);
      this.addLog(jobId, `Mode: ${mode}, Args: ${args.join(' ')}`);
      this.addLog(jobId, `Using batch compression mode`);

      // Start the compression process
      const child = spawn('bash', [scriptPath, ...args], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: process.cwd()
      });

      // Store process ID
      jobStatus.pid = child.pid;
      jobStatus.status = 'running';
      this.activeJobs.set(jobId, jobStatus);

      this.addLog(jobId, `Process started with PID: ${child.pid}`);

      // Handle stdout
      child.stdout.on('data', (data) => {
        const output = data.toString();
        const processedLogs = this.processLogOutput(output);

        // Add processed logs to job logs
        processedLogs.forEach(log => {
          this.addLog(jobId, log.message);

          // Emit real-time logs to frontend
          io.emit('compressionLog', {
            jobId: jobId,
            message: log.message,
            type: log.type || 'stdout'
          });
        });
      });

      // Handle stderr
      child.stderr.on('data', (data) => {
        const output = data.toString();
        this.addLog(jobId, `STDERR: ${output.trim()}`);

        // Emit real-time logs to frontend
        io.emit('compressionLog', {
          jobId: jobId,
          message: output.trim(),
          type: 'stderr'
        });
      });

      // Handle process completion
      child.on('close', (code) => {
        jobStatus.status = code === 0 ? 'completed' : 'failed';
        jobStatus.endTime = new Date();
        jobStatus.exitCode = code;

        this.addLog(jobId, `Process completed with exit code: ${code}`);

        // Clean up watchers
        this.cleanupJob(jobId);

        this.activeJobs.set(jobId, jobStatus);
        io.emit('compressionComplete', jobStatus);
      });

      // Handle process errors
      child.on('error', (error) => {
        jobStatus.status = 'failed';
        jobStatus.endTime = new Date();
        jobStatus.error = error.message;

        this.addLog(jobId, `Process error: ${error.message}`);

        this.activeJobs.set(jobId, jobStatus);
        io.emit('compressionError', {
          jobId: jobId,
          error: error.message
        });
      });

    } catch (error) {
      jobStatus.status = 'failed';
      jobStatus.endTime = new Date();
      jobStatus.error = error.message;

      this.addLog(jobId, `Startup error: ${error.message}`);

      this.activeJobs.set(jobId, jobStatus);
      io.emit('compressionError', {
        jobId: jobId,
        error: error.message
      });
    }

    return jobId;
  }

  /**
   * Get compression job status
   * @param {string} jobId - Job ID
   * @returns {object|null} - Job status
   */
  getCompressionStatus(jobId) {
    return this.activeJobs.get(jobId) || null;
  }

  /**
   * Cancel compression job
   * @param {string} jobId - Job ID
   * @returns {boolean} - Success status
   */
  cancelCompression(jobId) {
    const job = this.activeJobs.get(jobId);
    if (!job || !job.pid) {
      return false;
    }

    try {
      process.kill(job.pid, 'SIGTERM');
      job.status = 'cancelled';
      job.endTime = new Date();
      this.addLog(jobId, 'Compression cancelled by user');
      this.activeJobs.set(jobId, job);
      return true;
    } catch (error) {
      console.error('Failed to cancel compression:', error);
      return false;
    }
  }

  /**
   * Get compression logs
   * @param {string} jobId - Job ID
   * @returns {Array} - Log entries
   */
  getCompressionLogs(jobId) {
    return this.jobLogs.get(jobId) || [];
  }

  /**
   * Process log output to make it more readable
   * @param {string} rawOutput - Raw log output
   * @returns {Array} - Processed log entries
   */
  processLogOutput(rawOutput) {
    const logs = [];
    const lines = rawOutput.split('\n').filter(line => line.trim());

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      // Skip verbose upload metrics blocks
      if (trimmedLine.includes('===============================================================================')) {
        continue;
      }

      // Skip detailed upload metrics lines
      if (trimmedLine.match(/^📁 File Name:|^📏 Size:|^⏱️ Upload Duration:|^🚀 Upload Speed:|^📈 Running Total:|^🔢 Files Uploaded:|^⏰ Session Duration:|^📊 Average Speed:/)) {
        continue;
      }

      // Skip CloudWatch metrics lines
      if (trimmedLine.includes('📊 Published') && trimmedLine.includes('CloudWatch')) {
        continue;
      }

      // Process important messages
      let processedMessage = trimmedLine;
      let logType = 'info';

      // Clean up timestamps and emojis for better readability
      processedMessage = processedMessage.replace(/^\[\d{2}:\d{2}:\d{2}\]\s*/, '');

      // Categorize log types
      if (processedMessage.includes('⏱️ Uploaded segment:')) {
        // Keep the detailed segment upload message as-is
        logType = 'success';
      } else if (processedMessage.includes('📝 Uploaded playlist:')) {
        // Keep the playlist upload message as-is
        logType = 'success';
      } else if (processedMessage.includes('📝 PLAYLIST CREATED:')) {
        logType = 'info';
      } else if (processedMessage.includes('✅ PLAYLIST AVAILABLE:')) {
        logType = 'success';
      } else if (processedMessage.includes('🚀 UPLOADED:')) {
        // Legacy format - simplify if still present
        const match = processedMessage.match(/🚀 UPLOADED: (.+?) → s3:\/\/(.+?)\/(.+?)\/(.+)/);
        if (match) {
          processedMessage = `✅ Uploaded segment: ${match[1]}`;
          logType = 'success';
        }
      } else if (processedMessage.includes('Starting real-time HLS')) {
        processedMessage = '🎬 Starting HLS encoding...';
        logType = 'info';
      } else if (processedMessage.includes('HLS Streaming:')) {
        const match = processedMessage.match(/HLS Streaming: (.+?) -> (.+)/);
        if (match) {
          processedMessage = `📹 Processing: ${match[1].split('/').pop()}`;
          logType = 'info';
        }
      } else if (processedMessage.includes('Processing file')) {
        const match = processedMessage.match(/Processing file \d+: (.+)/);
        if (match) {
          processedMessage = `📁 Processing: ${match[1]}`;
          logType = 'info';
        }
      } else if (processedMessage.includes('✅') || processedMessage.includes('completed')) {
        logType = 'success';
      } else if (processedMessage.includes('❌') || processedMessage.includes('Error') || processedMessage.includes('Failed')) {
        logType = 'error';
      } else if (processedMessage.includes('⚠️') || processedMessage.includes('Warning')) {
        logType = 'warning';
      }

      // Remove excessive emojis but keep important ones
      processedMessage = processedMessage.replace(/[📊📁📏⏱️📈🔢⏰🎯📥📤🗂️👁️📁⚙️]/g, '');

      // Only add meaningful messages
      if (processedMessage.length > 5 && !processedMessage.match(/^[\s\-=]+$/)) {
        logs.push({
          message: processedMessage,
          type: logType
        });
      }
    }

    return logs;
  }

  /**
   * Add log entry
   * @param {string} jobId - Job ID
   * @param {string} message - Log message
   */
  addLog(jobId, message) {
    const logs = this.jobLogs.get(jobId) || [];
    logs.push({
      timestamp: new Date().toISOString(),
      message: message
    });
    this.jobLogs.set(jobId, logs);
    console.log(`[${jobId}] ${message}`);
  }

  /**
   * Clean up old jobs (call periodically)
   * @param {number} maxAge - Maximum age in milliseconds (default: 24 hours)
   */
  cleanupOldJobs(maxAge = 24 * 60 * 60 * 1000) {
    const now = new Date();
    for (const [jobId, job] of this.activeJobs.entries()) {
      if (job.endTime && (now - job.endTime) > maxAge) {
        this.activeJobs.delete(jobId);
        this.jobLogs.delete(jobId);
      }
    }
  }

  /**
   * Get all active jobs
   * @returns {Array} - List of active jobs
   */
  getAllJobs() {
    return Array.from(this.activeJobs.values());
  }

  /**
   * Detect input files and emit to frontend
   * @param {string} jobId - Job ID
   * @param {string} inputPath - Input path
   * @param {string} mode - Compression mode
   * @param {object} io - Socket.io instance
   */
  async detectAndEmitInputFiles(jobId, inputPath, mode, io) {
    try {
      let inputFiles = [];

      // For batch mode, scan directory for Y4M files
      if (!inputPath.startsWith('s3://') && fs.existsSync(inputPath)) {
          const files = fs.readdirSync(inputPath);
          const y4mFiles = files.filter(file => file.toLowerCase().endsWith('.y4m'));

          for (const file of y4mFiles) {
            const filePath = path.join(inputPath, file);
            const fileInfo = await this.prepareFileForViewing(filePath);
            inputFiles.push(fileInfo);
          }
        }

      // Update job status
      const job = this.activeJobs.get(jobId);
      if (job) {
        job.inputFiles = inputFiles;
        this.activeJobs.set(jobId, job);
      }

      // Emit input files to frontend
      io.emit('inputFilesDetected', {
        jobId: jobId,
        inputFiles: inputFiles,
        mode: mode
      });

      this.addLog(jobId, `Detected ${inputFiles.length} input file(s)`);
    } catch (error) {
      this.addLog(jobId, `Error detecting input files: ${error.message}`);
    }
  }

  /**
   * Prepare file for viewing (create preview if needed)
   * @param {string} filePath - File path
   * @returns {object} - File information with preview URL
   */
  async prepareFileForViewing(filePath) {
    const stats = fs.statSync(filePath);
    const filename = path.basename(filePath);
    const isY4M = filename.toLowerCase().endsWith('.y4m');

    let fileInfo = {
      filename: filename,
      path: filePath,
      size: stats.size,
      url: this.getFileUrl(filePath),
      isY4M: isY4M
    };

    // Create preview for Y4M files
    if (isY4M) {
      const previewPath = await this.createWebPreview(filePath);
      if (previewPath) {
        fileInfo.previewUrl = this.getFileUrl(previewPath);
      }
    }

    return fileInfo;
  }

  /**
   * Create web-compatible preview for Y4M files
   * @param {string} y4mPath - Y4M file path
   * @returns {string|null} - Preview file path or null if failed
   */
  async createWebPreview(y4mPath) {
    return new Promise((resolve) => {
      const filename = path.basename(y4mPath, '.y4m');
      const previewFilename = `${filename}_preview.mp4`;
      const previewPath = path.join(path.dirname(y4mPath), previewFilename);

      // Check if preview already exists
      if (fs.existsSync(previewPath)) {
        resolve(previewPath);
        return;
      }

      console.log(`Creating web preview: ${y4mPath} -> ${previewPath}`);

      // Convert Y4M to MP4 for web playback
      const { spawn } = require('child_process');
      const ffmpeg = spawn('ffmpeg', [
        '-i', y4mPath,
        '-c:v', 'libx264',
        '-preset', 'fast',
        '-crf', '23',
        '-c:a', 'aac',
        '-b:a', '128k',
        '-movflags', '+faststart',
        '-y',
        previewPath
      ]);

      let stderr = '';

      ffmpeg.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      ffmpeg.on('close', (code) => {
        if (code === 0 && fs.existsSync(previewPath)) {
          console.log(`Preview created successfully: ${previewPath}`);
          resolve(previewPath);
        } else {
          console.error(`Preview creation failed with code ${code}`);
          console.error('FFmpeg stderr:', stderr);
          resolve(null);
        }
      });

      ffmpeg.on('error', (error) => {
        console.error('FFmpeg error:', error);
        resolve(null);
      });
    });
  }

  /**
   * Start monitoring output directory for new files
   * @param {string} jobId - Job ID
   * @param {string} outputPath - Output path
   * @param {string} mode - Compression mode
   * @param {object} io - Socket.io instance
   */
  startOutputMonitoring(jobId, outputPath, mode, io) {
    // Don't monitor S3 paths or if output path doesn't exist yet
    if (outputPath.startsWith('s3://')) {
      this.addLog(jobId, 'S3 output monitoring not implemented');
      return;
    }

    // Ensure output directory exists
    if (!fs.existsSync(outputPath)) {
      // For batch mode, outputPath is a directory
      fs.mkdirSync(outputPath, { recursive: true });
    }

    const watchPath = outputPath;

    this.addLog(jobId, `Starting output monitoring: ${watchPath}`);

    // Watch for file changes in output directory
    const watcher = fs.watch(watchPath, (eventType, filename) => {
      if (eventType === 'rename' && filename) {
        const filePath = path.join(watchPath, filename);

        // Check if it's a video file and it exists (not deleted)
        if (fs.existsSync(filePath) && this.isVideoFile(filename)) {
          // Wait a moment for file to be fully written
          setTimeout(async () => {
            if (fs.existsSync(filePath)) {
              const stats = fs.statSync(filePath);
              const outputFileInfo = await this.prepareFileForViewing(filePath);

              outputFileInfo.size = stats.size; // Update with current size

              // Update job status
              const job = this.activeJobs.get(jobId);
              if (job) {
                if (!job.outputFiles) {
                  job.outputFiles = [];
                }
                job.outputFiles.push(outputFileInfo);
                this.activeJobs.set(jobId, job);
              }

              // Emit new output file to frontend
              io.emit('outputFileCreated', {
                jobId: jobId,
                outputFile: outputFileInfo,
                mode: mode
              });

              this.addLog(jobId, `New output file detected: ${filename}`);
            }
          }, 2000); // Wait 2 seconds for file to be fully written
        }
      }
    });

    // Store watcher reference for cleanup
    const job = this.activeJobs.get(jobId);
    if (job) {
      job.outputWatcher = watcher;
      this.activeJobs.set(jobId, job);
    }
  }

  /**
   * Get file URL for serving
   * @param {string} filePath - File path
   * @returns {string} - URL for serving the file
   */
  getFileUrl(filePath) {
    const relativePath = path.relative(process.cwd(), filePath);
    const filename = path.basename(filePath);

    if (relativePath.startsWith('uploads')) {
      return `/videos/${filename}`;
    } else if (relativePath.startsWith('compressed')) {
      return `/compressed/${filename}`;
    } else {
      // For other paths, determine based on directory
      const dir = path.dirname(filePath);
      if (dir.includes('uploads')) {
        return `/videos/${filename}`;
      } else if (dir.includes('compressed')) {
        return `/compressed/${filename}`;
      } else {
        return `/videos/${filename}`;
      }
    }
  }

  /**
   * Check if file is a video file
   * @param {string} filename - Filename
   * @returns {boolean} - Whether file is a video
   */
  isVideoFile(filename) {
    const videoExtensions = ['.y4m', '.mp4', '.avi', '.mov', '.mkv', '.webm'];
    const ext = path.extname(filename).toLowerCase();
    return videoExtensions.includes(ext);
  }

  /**
   * Clean up job resources
   * @param {string} jobId - Job ID
   */
  cleanupJob(jobId) {
    const job = this.activeJobs.get(jobId);
    if (job && job.outputWatcher) {
      job.outputWatcher.close();
      this.addLog(jobId, 'Output monitoring stopped');
    }
  }
}

module.exports = new CompressionService();
