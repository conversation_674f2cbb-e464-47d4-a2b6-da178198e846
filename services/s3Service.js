const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');

// Configure AWS SDK
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || 'us-east-1'
});

const s3 = new AWS.S3();

class S3Service {
  /**
   * Download a file from S3 to local storage
   * @param {string} bucket - S3 bucket name
   * @param {string} key - S3 object key
   * @returns {Promise<string>} - Local file path
   */
  async downloadFile(bucket, key) {
    try {
      const filename = path.basename(key);
      const localPath = path.join('uploads', `s3-${Date.now()}-${filename}`);
      
      console.log(`Downloading s3://${bucket}/${key} to ${localPath}`);
      
      const params = {
        Bucket: bucket,
        Key: key
      };

      // Check if object exists
      await s3.headObject(params).promise();
      
      // Download the file
      const data = await s3.getObject(params).promise();
      
      // Write to local file
      fs.writeFileSync(localPath, data.Body);
      
      console.log(`Successfully downloaded to ${localPath}`);
      return localPath;
    } catch (error) {
      console.error('S3 download error:', error);
      throw new Error(`Failed to download from S3: ${error.message}`);
    }
  }

  /**
   * Upload a file to S3
   * @param {string} localPath - Local file path
   * @param {string} bucket - S3 bucket name
   * @param {string} key - S3 object key
   * @returns {Promise<string>} - S3 URI
   */
  async uploadFile(localPath, bucket, key) {
    try {
      console.log(`Uploading ${localPath} to s3://${bucket}/${key}`);
      
      const fileContent = fs.readFileSync(localPath);
      
      const params = {
        Bucket: bucket,
        Key: key,
        Body: fileContent,
        ContentType: this.getContentType(localPath)
      };

      const result = await s3.upload(params).promise();
      
      console.log(`Successfully uploaded to ${result.Location}`);
      return result.Location;
    } catch (error) {
      console.error('S3 upload error:', error);
      throw new Error(`Failed to upload to S3: ${error.message}`);
    }
  }

  /**
   * List objects in S3 bucket with prefix
   * @param {string} bucket - S3 bucket name
   * @param {string} prefix - Object key prefix
   * @returns {Promise<Array>} - List of objects
   */
  async listObjects(bucket, prefix = '') {
    try {
      const params = {
        Bucket: bucket,
        Prefix: prefix
      };

      const data = await s3.listObjectsV2(params).promise();
      
      return data.Contents.map(obj => ({
        key: obj.Key,
        size: obj.Size,
        lastModified: obj.LastModified,
        uri: `s3://${bucket}/${obj.Key}`
      }));
    } catch (error) {
      console.error('S3 list error:', error);
      throw new Error(`Failed to list S3 objects: ${error.message}`);
    }
  }

  /**
   * Check if S3 object exists
   * @param {string} bucket - S3 bucket name
   * @param {string} key - S3 object key
   * @returns {Promise<boolean>} - Whether object exists
   */
  async objectExists(bucket, key) {
    try {
      await s3.headObject({ Bucket: bucket, Key: key }).promise();
      return true;
    } catch (error) {
      if (error.code === 'NotFound') {
        return false;
      }
      throw error;
    }
  }

  /**
   * Get content type based on file extension
   * @param {string} filePath - File path
   * @returns {string} - Content type
   */
  getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const contentTypes = {
      '.y4m': 'video/x-yuv4mpeg',
      '.mp4': 'video/mp4',
      '.avi': 'video/x-msvideo',
      '.mov': 'video/quicktime',
      '.mkv': 'video/x-matroska',
      '.webm': 'video/webm'
    };
    
    return contentTypes[ext] || 'application/octet-stream';
  }

  /**
   * Generate presigned URL for S3 object
   * @param {string} bucket - S3 bucket name
   * @param {string} key - S3 object key
   * @param {number} expires - Expiration time in seconds (default: 3600)
   * @returns {Promise<string>} - Presigned URL
   */
  async getPresignedUrl(bucket, key, expires = 3600) {
    try {
      const params = {
        Bucket: bucket,
        Key: key,
        Expires: expires
      };

      return await s3.getSignedUrlPromise('getObject', params);
    } catch (error) {
      console.error('Presigned URL error:', error);
      throw new Error(`Failed to generate presigned URL: ${error.message}`);
    }
  }

  /**
   * Get object stream from S3
   * @param {string} bucket - S3 bucket name
   * @param {string} key - S3 object key
   * @returns {Promise<Stream>} - S3 object stream
   */
  async getObjectStream(bucket, key) {
    try {
      const params = {
        Bucket: bucket,
        Key: key
      };

      return s3.getObject(params).createReadStream();
    } catch (error) {
      console.error('S3 stream error:', error);
      throw new Error(`Failed to get S3 stream: ${error.message}`);
    }
  }
}

module.exports = new S3Service();
