#!/usr/bin/env python3
"""
Comprehensive AWS S3 Bandwidth and Cost Monitoring System
Integrates with HLS streaming for real-time monitoring and cost tracking
"""

import boto3
import json
import time
import gzip
from datetime import datetime, timedelta
from collections import defaultdict
import threading
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AWSMonitoringIntegration:
    """
    Comprehensive AWS monitoring integration for S3 bandwidth and costs
    """
    
    def __init__(self, region='us-west-1'):
        self.region = region
        self.s3 = boto3.client('s3', region_name=region)
        self.cloudwatch = boto3.client('cloudwatch', region_name=region)
        self.ce = boto3.client('ce', region_name='us-east-1')  # Cost Explorer is in us-east-1
        
        # Monitoring configuration
        self.monitoring_enabled = True
        self.cost_threshold_daily = 10.0  # $10 daily threshold
        self.bandwidth_threshold_hourly = 1024**3  # 1 GB hourly threshold
        
        # Metrics storage
        self.session_metrics = {
            'start_time': None,
            'total_files': 0,
            'total_bytes': 0,
            'upload_speeds': [],
            'cost_estimates': []
        }
    
    def start_monitoring_session(self, session_name):
        """
        Start a new monitoring session
        """
        self.session_metrics['start_time'] = datetime.now()
        self.session_metrics['session_name'] = session_name
        
        logger.info(f"🎯 Started monitoring session: {session_name}")
        
        # Create CloudWatch custom metrics namespace
        self.publish_session_start_metric(session_name)
    
    def track_upload(self, filename, size_bytes, duration_seconds, bucket_name):
        """
        Track individual file upload with comprehensive metrics
        """
        self.session_metrics['total_files'] += 1
        self.session_metrics['total_bytes'] += size_bytes
        
        # Calculate upload speed
        speed_mbps = (size_bytes / 1024 / 1024) / duration_seconds if duration_seconds > 0 else 0
        self.session_metrics['upload_speeds'].append(speed_mbps)
        
        # Estimate costs for this upload
        cost_estimate = self.estimate_upload_cost(size_bytes, bucket_name)
        self.session_metrics['cost_estimates'].append(cost_estimate)
        
        # Publish real-time metrics to CloudWatch
        self.publish_upload_metrics(filename, size_bytes, speed_mbps, bucket_name)
        
        # Check thresholds
        self.check_cost_thresholds()
        self.check_bandwidth_thresholds()
        
        logger.info(f"📊 Tracked upload: {filename} ({size_bytes:,} bytes, {speed_mbps:.2f} MB/s)")
    
    def publish_upload_metrics(self, filename, size_bytes, speed_mbps, bucket_name):
        """
        Publish upload metrics to CloudWatch
        """
        try:
            metrics = [
                {
                    'MetricName': 'FileUploaded',
                    'Value': 1,
                    'Unit': 'Count',
                    'Dimensions': [
                        {'Name': 'BucketName', 'Value': bucket_name},
                        {'Name': 'SessionName', 'Value': self.session_metrics.get('session_name', 'Unknown')}
                    ]
                },
                {
                    'MetricName': 'BytesUploaded',
                    'Value': size_bytes,
                    'Unit': 'Bytes',
                    'Dimensions': [
                        {'Name': 'BucketName', 'Value': bucket_name},
                        {'Name': 'SessionName', 'Value': self.session_metrics.get('session_name', 'Unknown')}
                    ]
                },
                {
                    'MetricName': 'UploadSpeed',
                    'Value': speed_mbps,
                    'Unit': 'Count/Second',
                    'Dimensions': [
                        {'Name': 'BucketName', 'Value': bucket_name},
                        {'Name': 'SessionName', 'Value': self.session_metrics.get('session_name', 'Unknown')}
                    ]
                }
            ]
            
            self.cloudwatch.put_metric_data(
                Namespace='HLS/Streaming/Detailed',
                MetricData=metrics
            )
            
        except Exception as e:
            logger.error(f"Failed to publish CloudWatch metrics: {e}")
    
    def publish_session_start_metric(self, session_name):
        """
        Publish session start metric
        """
        try:
            self.cloudwatch.put_metric_data(
                Namespace='HLS/Streaming/Sessions',
                MetricData=[
                    {
                        'MetricName': 'SessionStarted',
                        'Value': 1,
                        'Unit': 'Count',
                        'Dimensions': [
                            {'Name': 'SessionName', 'Value': session_name}
                        ]
                    }
                ]
            )
        except Exception as e:
            logger.error(f"Failed to publish session start metric: {e}")
    
    def estimate_upload_cost(self, size_bytes, bucket_name):
        """
        Estimate cost for upload operation
        """
        # S3 pricing estimates (verify current rates)
        pricing = {
            'put_request': 0.005 / 1000,  # $0.005 per 1000 PUT requests
            'storage_gb_month': 0.023,    # $0.023 per GB per month
            'data_transfer_in': 0.0       # Data transfer in is free
        }
        
        # Calculate costs
        put_cost = pricing['put_request']
        storage_cost = (size_bytes / 1024**3) * pricing['storage_gb_month'] / 30  # Daily storage cost
        
        total_cost = put_cost + storage_cost
        
        return {
            'put_cost': put_cost,
            'storage_cost': storage_cost,
            'total_cost': total_cost,
            'size_gb': size_bytes / 1024**3
        }
    
    def check_cost_thresholds(self):
        """
        Check if cost thresholds are exceeded
        """
        total_estimated_cost = sum(est['total_cost'] for est in self.session_metrics['cost_estimates'])
        
        if total_estimated_cost > self.cost_threshold_daily:
            logger.warning(f"💰 Cost threshold exceeded: ${total_estimated_cost:.4f} > ${self.cost_threshold_daily}")
            self.send_cost_alert(total_estimated_cost)
    
    def check_bandwidth_thresholds(self):
        """
        Check if bandwidth thresholds are exceeded
        """
        if not self.session_metrics['start_time']:
            return
            
        session_duration = (datetime.now() - self.session_metrics['start_time']).total_seconds()
        hourly_bytes = self.session_metrics['total_bytes'] * (3600 / session_duration) if session_duration > 0 else 0
        
        if hourly_bytes > self.bandwidth_threshold_hourly:
            logger.warning(f"📊 Bandwidth threshold exceeded: {hourly_bytes/1024**3:.2f} GB/hour")
            self.send_bandwidth_alert(hourly_bytes)
    
    def send_cost_alert(self, current_cost):
        """
        Send cost alert (implement SNS integration)
        """
        logger.warning(f"🚨 COST ALERT: Current session cost ${current_cost:.4f} exceeds threshold ${self.cost_threshold_daily}")
        # TODO: Implement SNS notification
    
    def send_bandwidth_alert(self, hourly_bytes):
        """
        Send bandwidth alert (implement SNS integration)
        """
        logger.warning(f"🚨 BANDWIDTH ALERT: Current rate {hourly_bytes/1024**3:.2f} GB/hour exceeds threshold")
        # TODO: Implement SNS notification
    
    def get_session_summary(self):
        """
        Get comprehensive session summary
        """
        if not self.session_metrics['start_time']:
            return None
            
        duration = (datetime.now() - self.session_metrics['start_time']).total_seconds()
        avg_speed = sum(self.session_metrics['upload_speeds']) / len(self.session_metrics['upload_speeds']) if self.session_metrics['upload_speeds'] else 0
        total_cost = sum(est['total_cost'] for est in self.session_metrics['cost_estimates'])
        
        return {
            'session_name': self.session_metrics.get('session_name', 'Unknown'),
            'duration_seconds': duration,
            'total_files': self.session_metrics['total_files'],
            'total_bytes': self.session_metrics['total_bytes'],
            'total_mb': self.session_metrics['total_bytes'] / 1024 / 1024,
            'average_speed_mbps': avg_speed,
            'estimated_total_cost': total_cost,
            'cost_per_mb': total_cost / (self.session_metrics['total_bytes'] / 1024 / 1024) if self.session_metrics['total_bytes'] > 0 else 0
        }
    
    def publish_session_summary(self):
        """
        Publish final session summary to CloudWatch
        """
        summary = self.get_session_summary()
        if not summary:
            return
            
        try:
            metrics = [
                {
                    'MetricName': 'SessionDuration',
                    'Value': summary['duration_seconds'],
                    'Unit': 'Seconds',
                    'Dimensions': [
                        {'Name': 'SessionName', 'Value': summary['session_name']}
                    ]
                },
                {
                    'MetricName': 'SessionTotalFiles',
                    'Value': summary['total_files'],
                    'Unit': 'Count',
                    'Dimensions': [
                        {'Name': 'SessionName', 'Value': summary['session_name']}
                    ]
                },
                {
                    'MetricName': 'SessionTotalBytes',
                    'Value': summary['total_bytes'],
                    'Unit': 'Bytes',
                    'Dimensions': [
                        {'Name': 'SessionName', 'Value': summary['session_name']}
                    ]
                },
                {
                    'MetricName': 'SessionAverageSpeed',
                    'Value': summary['average_speed_mbps'],
                    'Unit': 'Count/Second',
                    'Dimensions': [
                        {'Name': 'SessionName', 'Value': summary['session_name']}
                    ]
                },
                {
                    'MetricName': 'SessionEstimatedCost',
                    'Value': summary['estimated_total_cost'],
                    'Unit': 'None',
                    'Dimensions': [
                        {'Name': 'SessionName', 'Value': summary['session_name']}
                    ]
                }
            ]
            
            self.cloudwatch.put_metric_data(
                Namespace='HLS/Streaming/Sessions',
                MetricData=metrics
            )
            
            logger.info(f"📊 Published session summary to CloudWatch")
            
        except Exception as e:
            logger.error(f"Failed to publish session summary: {e}")
    
    def generate_comprehensive_report(self):
        """
        Generate comprehensive monitoring report
        """
        summary = self.get_session_summary()
        if not summary:
            return
            
        print("="*80)
        print("🎯 COMPREHENSIVE AWS MONITORING REPORT")
        print("="*80)
        
        print(f"📊 Session: {summary['session_name']}")
        print(f"⏱️  Duration: {summary['duration_seconds']:.1f} seconds ({summary['duration_seconds']/60:.1f} minutes)")
        print(f"📁 Files Uploaded: {summary['total_files']:,}")
        print(f"📏 Total Data: {summary['total_bytes']:,} bytes ({summary['total_mb']:.2f} MB)")
        print(f"🚀 Average Speed: {summary['average_speed_mbps']:.2f} MB/s")
        print(f"💰 Estimated Cost: ${summary['estimated_total_cost']:.6f}")
        print(f"💵 Cost per MB: ${summary['cost_per_mb']:.8f}")
        
        # Cost breakdown
        print(f"\n💰 COST BREAKDOWN:")
        total_put_cost = sum(est['put_cost'] for est in self.session_metrics['cost_estimates'])
        total_storage_cost = sum(est['storage_cost'] for est in self.session_metrics['cost_estimates'])
        
        print(f"   PUT Requests: ${total_put_cost:.6f}")
        print(f"   Storage (daily): ${total_storage_cost:.6f}")
        print(f"   Total: ${summary['estimated_total_cost']:.6f}")
        
        # Performance metrics
        if self.session_metrics['upload_speeds']:
            speeds = self.session_metrics['upload_speeds']
            print(f"\n🚀 PERFORMANCE METRICS:")
            print(f"   Min Speed: {min(speeds):.2f} MB/s")
            print(f"   Max Speed: {max(speeds):.2f} MB/s")
            print(f"   Avg Speed: {sum(speeds)/len(speeds):.2f} MB/s")
        
        print("="*80)

# Integration with existing HLS streaming
class EnhancedHLSUploadTracker:
    """
    Enhanced upload tracker with AWS monitoring integration
    """
    
    def __init__(self, session_name, bucket_name):
        self.aws_monitor = AWSMonitoringIntegration()
        self.bucket_name = bucket_name
        
        # Start monitoring session
        self.aws_monitor.start_monitoring_session(session_name)
    
    def track_upload(self, filename, size_bytes, duration_seconds):
        """
        Track upload with AWS monitoring
        """
        self.aws_monitor.track_upload(filename, size_bytes, duration_seconds, self.bucket_name)
    
    def finalize_session(self):
        """
        Finalize monitoring session
        """
        self.aws_monitor.publish_session_summary()
        self.aws_monitor.generate_comprehensive_report()

# Example usage
if __name__ == "__main__":
    # Example integration
    tracker = EnhancedHLSUploadTracker("enhanced_logging_big_test", "zmt-compressed-video")
    
    # Simulate uploads (replace with actual upload tracking)
    import random
    for i in range(10):
        filename = f"stream_{i:04d}.ts"
        size_bytes = random.randint(50000, 500000)
        duration_seconds = random.uniform(0.2, 1.0)
        
        tracker.track_upload(filename, size_bytes, duration_seconds)
        time.sleep(0.1)  # Small delay
    
    # Finalize session
    tracker.finalize_session()
