#!/bin/bash

# Test S3 path construction
INPUT_SOURCE="s3://zmt-source-video/input/"
SOURCE_REGION="us-east-1"

echo "Testing S3 path construction..."
echo "INPUT_SOURCE: $INPUT_SOURCE"
echo "SOURCE_REGION: $SOURCE_REGION"
echo ""

# Remove trailing slash if present for consistent path handling
s3_path="${INPUT_SOURCE%/}"
echo "s3_path (no trailing slash): $s3_path"

# Extract bucket and prefix
bucket_and_prefix="${s3_path#s3://}"
bucket="${bucket_and_prefix%%/*}"
prefix="${bucket_and_prefix#*/}"

echo "bucket_and_prefix: $bucket_and_prefix"
echo "bucket: $bucket"
echo "prefix: $prefix"
echo ""

echo "Testing AWS S3 ls command..."
aws s3 ls "$s3_path/" --recursive --region "$SOURCE_REGION"
echo ""

echo "Testing file list construction..."
aws s3 ls "$s3_path/" --recursive --region "$SOURCE_REGION" | grep "\.y4m$" | awk '{print $4}' | while read -r file_path; do
    echo "Found file: $file_path"
    echo "Full S3 URI: s3://${bucket}/${file_path}"
done

echo ""
echo "Testing the actual function logic..."
# Simulate the get_file_list function
aws s3 ls "$s3_path/" --recursive --region "$SOURCE_REGION" | grep "\.y4m$" | awk '{print $4}' | while read -r file_path; do
    full_uri="s3://${bucket}/${file_path}"
    echo "Generated URI: $full_uri"

    # Test if this file exists
    echo "Testing access to: $full_uri"
    aws s3 ls "$full_uri" --region "$SOURCE_REGION"
done
