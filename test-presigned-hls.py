#!/usr/bin/env python3
"""
Test script for S3 Presigned HLS functionality
"""

import sys
import os
from s3_presigned_hls import S3PresignedHLSManager, create_presigned_hls_viewer

def test_presigned_hls():
    """Test presigned HLS functionality with existing S3 playlist"""
    
    print("🎯 Testing S3 Presigned HLS Functionality")
    print("=" * 50)
    
    # Use the playlist from our previous HLS streaming test
    s3_playlist_uri = "s3://zmt-compressed-video/hls/enhanced_logging_big_test/playlist.m3u8"
    
    print(f"📍 Testing with playlist: {s3_playlist_uri}")
    
    try:
        # Test 1: Create presigned HLS manager
        print("\n📊 Test 1: Creating S3PresignedHLSManager...")
        manager = S3PresignedHLSManager(region='us-west-1', expiration_hours=24)
        print("✅ S3PresignedHLSManager created successfully")
        
        # Test 2: Download original playlist
        print("\n📊 Test 2: Downloading original playlist...")
        original_content = manager.download_playlist_from_s3(s3_playlist_uri)
        
        if original_content:
            print("✅ Original playlist downloaded successfully")
            print(f"📄 Playlist content preview (first 200 chars):")
            print(f"   {original_content[:200]}...")
        else:
            print("❌ Failed to download original playlist")
            return False
        
        # Test 3: Create presigned playlist
        print("\n📊 Test 3: Creating presigned playlist...")
        presigned_content, playlist_url, segment_info = manager.create_presigned_playlist(
            s3_playlist_uri=s3_playlist_uri,
            output_path="./test_presigned_playlist.m3u8"
        )
        
        if presigned_content and segment_info:
            print("✅ Presigned playlist created successfully")
            print(f"📊 Segment info:")
            print(f"   • Total segments: {segment_info['total_segments']}")
            print(f"   • Total duration: {segment_info['playlist_duration']:.1f} seconds")
            print(f"🔗 Presigned playlist URL:")
            print(f"   {playlist_url}")
            print(f"💾 Saved to: ./test_presigned_playlist.m3u8")
        else:
            print("❌ Failed to create presigned playlist")
            return False
        
        # Test 4: Create complete streaming manifest
        print("\n📊 Test 4: Creating streaming manifest...")
        manifest = manager.create_streaming_manifest(
            s3_playlist_uri=s3_playlist_uri,
            output_dir="./test_presigned_hls"
        )
        
        if manifest:
            print("✅ Streaming manifest created successfully")
            print(f"📁 Files saved to: ./test_presigned_hls/")
            print(f"   • presigned_playlist.m3u8")
            print(f"   • streaming_manifest.json")
            print(f"⏰ URLs expire at: {manifest['expires_at']}")
        else:
            print("❌ Failed to create streaming manifest")
            return False
        
        # Test 5: Get streaming URLs for immediate use
        print("\n📊 Test 5: Getting streaming URLs...")
        streaming_urls = manager.get_streaming_urls(s3_playlist_uri)
        
        if streaming_urls:
            print("✅ Streaming URLs generated successfully")
            print(f"🔗 Direct playlist URL:")
            print(f"   {streaming_urls['playlist_url']}")
            print(f"📊 Streaming info:")
            print(f"   • Segments: {streaming_urls['total_segments']}")
            print(f"   • Duration: {streaming_urls['duration_seconds']:.1f}s")
            print(f"   • Expires: {streaming_urls['expires_at']}")
        else:
            print("❌ Failed to get streaming URLs")
            return False
        
        # Test 6: Verify presigned playlist content
        print("\n📊 Test 6: Verifying presigned playlist content...")
        
        # Check if presigned URLs are in the content
        presigned_url_count = presigned_content.count('https://')
        original_ts_count = original_content.count('.ts')
        
        print(f"📈 Verification results:")
        print(f"   • Original .ts files: {original_ts_count}")
        print(f"   • Presigned URLs: {presigned_url_count}")
        print(f"   • Match: {'✅' if presigned_url_count >= original_ts_count else '❌'}")
        
        if presigned_url_count >= original_ts_count:
            print("✅ All segment files have presigned URLs")
        else:
            print("⚠️  Some segment files may not have presigned URLs")
        
        print("\n🎉 All tests completed successfully!")
        print("\n🎯 Next steps:")
        print("1. Open hls_viewer.html in your browser")
        print("2. Use the presigned playlist URL:")
        print(f"   {streaming_urls['playlist_url']}")
        print("3. Or upload the presigned playlist file:")
        print("   ./test_presigned_hls/presigned_playlist.m3u8")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_convenience_function():
    """Test the convenience function"""
    
    print("\n" + "=" * 50)
    print("🎯 Testing Convenience Function")
    print("=" * 50)
    
    s3_playlist_uri = "s3://zmt-compressed-video/hls/enhanced_logging_big_test/playlist.m3u8"
    
    try:
        print(f"📍 Using convenience function with: {s3_playlist_uri}")
        
        manifest = create_presigned_hls_viewer(
            s3_playlist_uri=s3_playlist_uri,
            output_dir="./convenience_test",
            expiration_hours=12
        )
        
        if manifest:
            print("✅ Convenience function worked successfully")
            print(f"📁 Output directory: ./convenience_test")
            print(f"⏰ URLs expire in: 12 hours")
            print(f"🔗 Playlist URL: {manifest['presigned_playlist_url']}")
            return True
        else:
            print("❌ Convenience function failed")
            return False
            
    except Exception as e:
        print(f"❌ Convenience function test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 S3 Presigned HLS Testing Suite")
    print("=" * 40)
    
    # Check if we have the required playlist
    test_playlist = "s3://zmt-compressed-video/hls/enhanced_logging_big_test/playlist.m3u8"
    print(f"📋 Testing with playlist: {test_playlist}")
    print("💡 Make sure this playlist exists from previous HLS streaming tests")
    
    # Run main tests
    success1 = test_presigned_hls()
    
    # Run convenience function test
    success2 = test_convenience_function()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n🚀 Ready for secure HLS streaming!")
        print("\n📋 Files created:")
        print("   • test_presigned_playlist.m3u8")
        print("   • test_presigned_hls/presigned_playlist.m3u8")
        print("   • test_presigned_hls/streaming_manifest.json")
        print("   • convenience_test/presigned_playlist.m3u8")
        print("   • convenience_test/streaming_manifest.json")
        print("\n🌐 Open hls_viewer.html to test streaming!")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED")
        print("💡 Make sure:")
        print("   1. AWS credentials are configured")
        print("   2. The test playlist exists in S3")
        print("   3. You have S3 read permissions")
        sys.exit(1)
