#!/usr/bin/env python3

"""
Real-time HLS streaming to S3 script
Converts video to HLS segments and uploads them to S3 in real-time using watchdog
"""

import os
import sys
import time
import subprocess
import boto3
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import tempfile
import shutil
import json
from s3_presigned_hls import S3PresignedHLSManager

class UploadTracker:
    """Tracks comprehensive upload metrics and bandwidth usage"""

    def __init__(self, enable_aws_logging=False, session_name="hls_session"):
        self.total_bytes = 0
        self.total_files = 0
        self.upload_history = []
        self.session_start = datetime.now()
        self.start_time = time.time()  # For duration calculations

        # AWS CloudWatch integration
        self.enable_aws_logging = enable_aws_logging
        self.session_name = session_name
        if enable_aws_logging:
            try:
                self.cloudwatch = boto3.client('cloudwatch')
                print(f"✅ AWS CloudWatch integration enabled for session: {session_name}")
                self._publish_session_start()
            except Exception as e:
                print(f"⚠️  AWS CloudWatch integration failed: {e}")
                self.enable_aws_logging = False

    def log_upload(self, filename, file_size, upload_duration, upload_speed_mbps, bucket_name="unknown"):
        """Log a single upload with comprehensive metrics"""
        self.total_bytes += file_size
        self.total_files += 1

        upload_record = {
            'timestamp': datetime.now().isoformat(),
            'filename': filename,
            'size_bytes': file_size,
            'size_mb': file_size / (1024 * 1024),
            'duration_seconds': upload_duration,
            'speed_mbps': upload_speed_mbps,
            'running_total_bytes': self.total_bytes,
            'running_total_mb': self.total_bytes / (1024 * 1024),
            'file_number': self.total_files
        }

        self.upload_history.append(upload_record)

        # Send to AWS CloudWatch if enabled
        if self.enable_aws_logging:
            self._publish_upload_metrics(filename, file_size, upload_speed_mbps, bucket_name)

        # Log detailed metrics
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"\n{'='*80}")
        print(f"[{timestamp}] 📊 UPLOAD METRICS - File #{self.total_files}")
        print(f"{'='*80}")
        print(f"📁 File Name:        {filename}")
        print(f"📏 Size:             {file_size:,} bytes ({file_size / (1024 * 1024):.2f} MB)")
        print(f"⏱️  Upload Duration:  {upload_duration:.3f} seconds")
        print(f"🚀 Upload Speed:     {upload_speed_mbps:.2f} MB/s")
        print(f"📈 Running Total:    {self.total_bytes:,} bytes ({self.total_bytes / (1024 * 1024):.2f} MB)")
        print(f"🔢 Files Uploaded:   {self.total_files}")

        # Calculate session statistics
        session_duration = (datetime.now() - self.session_start).total_seconds()
        avg_speed = (self.total_bytes / (1024 * 1024)) / max(session_duration, 1)

        print(f"⏰ Session Duration: {session_duration:.1f} seconds")
        print(f"📊 Average Speed:    {avg_speed:.2f} MB/s")
        print(f"{'='*80}\n")

    def get_summary(self):
        """Get comprehensive session summary"""
        session_duration = (datetime.now() - self.session_start).total_seconds()
        avg_speed = (self.total_bytes / (1024 * 1024)) / max(session_duration, 1)

        return {
            'session_start': self.session_start.isoformat(),
            'session_end': datetime.now().isoformat(),
            'session_duration_seconds': session_duration,
            'total_files': self.total_files,
            'total_bytes': self.total_bytes,
            'total_mb': self.total_bytes / (1024 * 1024),
            'average_speed_mbps': avg_speed,
            'upload_history': self.upload_history
        }

    def save_report(self, output_path):
        """Save detailed upload report to JSON file"""
        report = self.get_summary()
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"📄 Upload report saved to: {output_path}")

    def _publish_session_start(self):
        """Publish session start metric to CloudWatch"""
        try:
            self.cloudwatch.put_metric_data(
                Namespace='HLS/Streaming/Sessions',
                MetricData=[
                    {
                        'MetricName': 'SessionStarted',
                        'Value': 1,
                        'Unit': 'Count',
                        'Dimensions': [
                            {'Name': 'SessionName', 'Value': self.session_name}
                        ]
                    }
                ]
            )
            print(f"📊 Published session start metric to CloudWatch")
        except Exception as e:
            print(f"⚠️  Failed to publish session start metric: {e}")

    def _publish_upload_metrics(self, filename, file_size, speed_mbps, bucket_name):
        """Publish upload metrics to CloudWatch"""
        try:
            metrics = [
                {
                    'MetricName': 'FileUploaded',
                    'Value': 1,
                    'Unit': 'Count',
                    'Dimensions': [
                        {'Name': 'BucketName', 'Value': bucket_name},
                        {'Name': 'SessionName', 'Value': self.session_name}
                    ]
                },
                {
                    'MetricName': 'BytesUploaded',
                    'Value': file_size,
                    'Unit': 'Bytes',
                    'Dimensions': [
                        {'Name': 'BucketName', 'Value': bucket_name},
                        {'Name': 'SessionName', 'Value': self.session_name}
                    ]
                },
                {
                    'MetricName': 'UploadSpeed',
                    'Value': speed_mbps,
                    'Unit': 'Count/Second',
                    'Dimensions': [
                        {'Name': 'BucketName', 'Value': bucket_name},
                        {'Name': 'SessionName', 'Value': self.session_name}
                    ]
                }
            ]

            self.cloudwatch.put_metric_data(
                Namespace='HLS/Streaming/Detailed',
                MetricData=metrics
            )
            print(f"📊 Published upload metrics to CloudWatch for {filename}")
        except Exception as e:
            print(f"⚠️  Failed to publish upload metrics: {e}")

    def publish_session_summary(self):
        """Publish final session summary to CloudWatch"""
        if not self.enable_aws_logging:
            return

        summary = self.get_summary()
        try:
            metrics = [
                {
                    'MetricName': 'SessionDuration',
                    'Value': summary['session_duration_seconds'],
                    'Unit': 'Seconds',
                    'Dimensions': [
                        {'Name': 'SessionName', 'Value': self.session_name}
                    ]
                },
                {
                    'MetricName': 'SessionTotalFiles',
                    'Value': summary['total_files'],
                    'Unit': 'Count',
                    'Dimensions': [
                        {'Name': 'SessionName', 'Value': self.session_name}
                    ]
                },
                {
                    'MetricName': 'SessionTotalBytes',
                    'Value': summary['total_bytes'],
                    'Unit': 'Bytes',
                    'Dimensions': [
                        {'Name': 'SessionName', 'Value': self.session_name}
                    ]
                },
                {
                    'MetricName': 'SessionAverageSpeed',
                    'Value': summary['average_speed_mbps'],
                    'Unit': 'Count/Second',
                    'Dimensions': [
                        {'Name': 'SessionName', 'Value': self.session_name}
                    ]
                }
            ]

            self.cloudwatch.put_metric_data(
                Namespace='HLS/Streaming/Sessions',
                MetricData=metrics
            )
            print(f"📊 Published session summary to CloudWatch")
        except Exception as e:
            print(f"⚠️  Failed to publish session summary: {e}")

class HLSUploadHandler(FileSystemEventHandler):
    """Handles file system events for HLS segment uploads"""

    def __init__(self, s3_client, bucket_name, s3_prefix, region):
        self.s3_client = s3_client
        self.bucket_name = bucket_name
        self.s3_prefix = s3_prefix
        self.region = region
        self.uploaded_files = set()
        # Enable AWS logging with session name based on S3 prefix
        session_name = s3_prefix.replace('/', '_').strip('_') or 'hls_session'
        self.upload_tracker = UploadTracker(enable_aws_logging=True, session_name=session_name)
        
    def log_message(self, message):
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
        
    def upload_file_to_s3(self, local_path, s3_key):
        """Upload a single file to S3 using PUT with comprehensive tracking"""
        try:
            file_size = os.path.getsize(local_path)
            filename = os.path.basename(local_path)

            # Upload file with timing
            start_time = time.time()
            self.s3_client.upload_file(
                local_path,
                self.bucket_name,
                s3_key
            )
            upload_duration = time.time() - start_time

            # Calculate throughput
            upload_speed_mbps = (file_size / (1024 * 1024)) / max(upload_duration, 0.001)

            # Log to upload tracker with bucket name for AWS metrics
            self.upload_tracker.log_upload(filename, file_size, upload_duration, upload_speed_mbps, self.bucket_name)

            # Format file size for display
            if file_size >= 1024 * 1024:
                size_display = f"{file_size:,} bytes ({file_size / (1024 * 1024):.2f} MB)"
            elif file_size >= 1024:
                size_display = f"{file_size:,} bytes ({file_size / 1024:.2f} KB)"
            else:
                size_display = f"{file_size:,} bytes"

            # Calculate session duration
            session_duration = time.time() - self.upload_tracker.start_time

            # Enhanced log message with detailed metrics
            if filename.endswith('.m3u8'):
                # Special message for playlist files
                self.log_message(f"📝 Uploaded playlist: {filename} | Size: {size_display} | Upload Speed: {upload_speed_mbps:.2f} MB/s")
            else:
                # Detailed message for segment files
                self.log_message(f"⏱️ Uploaded segment: {filename} | Size: {size_display} | Upload Speed: {upload_speed_mbps:.2f} MB/s | Running Total: {self.upload_tracker.total_bytes / (1024 * 1024):.2f} MB | Session Duration: {session_duration:.1f} seconds")

            return True

        except Exception as e:
            self.log_message(f"❌ UPLOAD FAILED: {local_path} - {str(e)}")
            return False
    
    def on_created(self, event):
        """Handle file creation events"""
        if event.is_directory:
            return

        file_path = event.src_path
        filename = os.path.basename(file_path)

        # Only process .ts segments and .m3u8 playlists
        if not (filename.endswith('.ts') or filename.endswith('.m3u8')):
            return

        # For .m3u8 files, handle immediately and aggressively
        if filename.endswith('.m3u8'):
            self.log_message(f"📝 PLAYLIST DETECTED: {filename} - Processing immediately...")
            self.handle_playlist_upload(file_path, filename)
            return

        # Avoid duplicate uploads for .ts files
        if file_path in self.uploaded_files:
            return

        # Quick wait for .ts files
        time.sleep(0.1)

        # Check if file exists and has content
        if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
            return

        # Upload to S3
        s3_key = f"{self.s3_prefix.rstrip('/')}/{filename}" if self.s3_prefix else filename

        if self.upload_file_to_s3(file_path, s3_key):
            self.uploaded_files.add(file_path)
            
    def handle_playlist_upload(self, file_path, filename):
        """Handle playlist upload with retries and proper timing"""
        max_retries = 5
        retry_delay = 0.2

        for attempt in range(max_retries):
            try:
                # Wait for file to be written
                time.sleep(retry_delay * (attempt + 1))

                # Check if file exists and has content
                if not os.path.exists(file_path):
                    self.log_message(f"⏳ Playlist file not ready yet (attempt {attempt + 1}/{max_retries})")
                    continue

                file_size = os.path.getsize(file_path)
                if file_size == 0:
                    self.log_message(f"⏳ Playlist file empty, waiting... (attempt {attempt + 1}/{max_retries})")
                    continue

                # Avoid duplicate uploads
                if file_path in self.uploaded_files:
                    self.log_message(f"📝 Playlist already uploaded: {filename}")
                    return

                # Upload to S3
                s3_key = f"{self.s3_prefix.rstrip('/')}/{filename}" if self.s3_prefix else filename

                self.log_message(f"📝 UPLOADING PLAYLIST: {filename} ({file_size} bytes)")
                if self.upload_file_to_s3(file_path, s3_key):
                    self.uploaded_files.add(file_path)
                    self.log_message(f"✅ PLAYLIST AVAILABLE: Stream ready for playback at s3://{self.bucket_name}/{s3_key}")
                    return
                else:
                    self.log_message(f"❌ Playlist upload failed (attempt {attempt + 1}/{max_retries})")

            except Exception as e:
                self.log_message(f"❌ Playlist upload error (attempt {attempt + 1}/{max_retries}): {e}")

        self.log_message(f"❌ Failed to upload playlist after {max_retries} attempts")

    def on_modified(self, event):
        """Handle file modification events (for playlist updates)"""
        if event.is_directory:
            return

        file_path = event.src_path
        filename = os.path.basename(file_path)

        # Only process .m3u8 playlist updates
        if not filename.endswith('.m3u8'):
            return

        # Handle playlist upload (this catches cases where on_created missed it)
        self.log_message(f"📝 PLAYLIST MODIFIED: {filename} - Uploading...")
        self.handle_playlist_upload(file_path, filename)

def parse_s3_path(s3_path):
    """Parse S3 path into bucket and key components"""
    if not s3_path.startswith('s3://'):
        raise ValueError("S3 path must start with s3://")
    
    path_parts = s3_path[5:].split('/', 1)
    bucket = path_parts[0]
    key = path_parts[1] if len(path_parts) > 1 else ""
    
    return bucket, key

def start_ffmpeg_hls(input_source, output_dir, source_region=None):
    """Start FFmpeg HLS encoding process"""
    
    # Prepare input command
    if input_source.startswith('s3://'):
        # S3 input - stream from S3
        input_cmd = [
            'aws', 's3', 'cp', input_source, '-'
        ]
        if source_region:
            input_cmd.extend(['--region', source_region])
        
        ffmpeg_input = 'pipe:0'
    else:
        # Local file input
        input_cmd = None
        ffmpeg_input = input_source
    
    # FFmpeg HLS command
    ffmpeg_cmd = [
        'ffmpeg',
        '-y',  # Overwrite output files
        '-hide_banner',
        '-loglevel', 'error',  # Quiet logging
        '-i', ffmpeg_input,
        '-c:v', 'libx264',     # Video codec
        '-crf', '28',          # Quality setting
        '-preset', 'superfast', # Encoding speed
        '-c:a', 'aac',         # Audio codec
        '-f', 'hls',           # HLS format
        '-hls_time', '2',      # 2-second segments
        '-hls_list_size', '0', # Keep all segments in playlist
        '-start_number', '0',  # Start numbering from 0
        '-hls_segment_filename', os.path.join(output_dir, 'stream_%04d.ts'),
        os.path.join(output_dir, 'playlist.m3u8')
    ]
    
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎬 Starting HLS encoding...")
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 📁 Output directory: {output_dir}")
    print(f"[{datetime.now().strftime('%H:%M:%S')}] ⚙️  HLS settings: 2s segments, unlimited playlist")
    
    # Start the process
    if input_cmd:
        # Pipe from S3 to FFmpeg
        s3_process = subprocess.Popen(input_cmd, stdout=subprocess.PIPE)
        ffmpeg_process = subprocess.Popen(ffmpeg_cmd, stdin=s3_process.stdout, 
                                        stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        s3_process.stdout.close()  # Allow s3_process to receive SIGPIPE
        return ffmpeg_process, s3_process
    else:
        # Direct file input
        ffmpeg_process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return ffmpeg_process, None

def main():
    if len(sys.argv) < 3:
        print("Usage: python3 hls_streaming_s3.py <input_source> <s3_output_path> [source_region] [dest_region]")
        print("")
        print("Examples:")
        print("  python3 hls_streaming_s3.py /path/to/video.y4m s3://bucket/output/stream/ us-east-1 us-west-1")
        print("  python3 hls_streaming_s3.py s3://bucket/input/video.y4m s3://bucket/output/stream/ us-east-1 us-west-1")
        sys.exit(1)
    
    input_source = sys.argv[1]
    s3_output_path = sys.argv[2]
    source_region = sys.argv[3] if len(sys.argv) > 3 else None
    dest_region = sys.argv[4] if len(sys.argv) > 4 else source_region
    
    # Parse S3 output path
    try:
        bucket_name, s3_prefix = parse_s3_path(s3_output_path)
    except ValueError as e:
        print(f"Error: {e}")
        sys.exit(1)
    
    # Create S3 client
    s3_client = boto3.client('s3', region_name=dest_region)
    
    # Create temporary directory for HLS output
    temp_dir = tempfile.mkdtemp(prefix='hls_streaming_')
    
    try:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎯 Starting real-time HLS streaming to S3")
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 📥 Input: {input_source}")
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 📤 Output: s3://{bucket_name}/{s3_prefix}")
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🗂️  Temp dir: {temp_dir}")
        
        # Set up file watcher
        event_handler = HLSUploadHandler(s3_client, bucket_name, s3_prefix, dest_region)
        observer = Observer()
        observer.schedule(event_handler, temp_dir, recursive=False)
        observer.start()
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 👁️  File watcher started")
        
        # Start FFmpeg HLS encoding
        ffmpeg_process, _ = start_ffmpeg_hls(input_source, temp_dir, source_region)

        # Monitor for playlist creation in a separate thread
        import threading

        def monitor_playlist():
            playlist_path = os.path.join(temp_dir, 'playlist.m3u8')
            max_wait = 30  # Wait up to 30 seconds for playlist
            check_interval = 0.5

            for i in range(int(max_wait / check_interval)):
                if os.path.exists(playlist_path) and os.path.getsize(playlist_path) > 0:
                    # Give file watcher a moment to handle it
                    time.sleep(1)

                    # If file watcher hasn't uploaded it yet, do it manually
                    if playlist_path not in event_handler.uploaded_files:
                        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🚨 MANUAL PLAYLIST UPLOAD: File watcher missed it")
                        event_handler.handle_playlist_upload(playlist_path, 'playlist.m3u8')
                    break
                time.sleep(check_interval)

        # Start playlist monitoring thread
        playlist_thread = threading.Thread(target=monitor_playlist, daemon=True)
        playlist_thread.start()

        # Wait for FFmpeg to complete
        _, stderr = ffmpeg_process.communicate()

        # Check results
        if ffmpeg_process.returncode == 0:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ HLS encoding completed successfully")
        else:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ FFmpeg failed with return code {ffmpeg_process.returncode}")
            if stderr:
                print(f"Error: {stderr.decode()}")

        # Wait a moment for final uploads
        time.sleep(2)

        # Note: Playlist is now uploaded immediately when created by the file watcher
        
        # Stop file watcher
        observer.stop()
        observer.join()

        # Generate final upload report
        print(f"\n{'='*80}")
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎯 HLS STREAMING COMPLETED")
        print(f"{'='*80}")

        # Publish session summary to AWS CloudWatch
        event_handler.upload_tracker.publish_session_summary()

        summary = event_handler.upload_tracker.get_summary()
        print(f"📊 FINAL SESSION SUMMARY:")
        print(f"   • Total Files Uploaded: {summary['total_files']}")
        print(f"   • Total Data Uploaded:  {summary['total_bytes']:,} bytes ({summary['total_mb']:.2f} MB)")
        print(f"   • Session Duration:     {summary['session_duration_seconds']:.1f} seconds")
        print(f"   • Average Upload Speed: {summary['average_speed_mbps']:.2f} MB/s")
        print(f"   • Output Location:      s3://{bucket_name}/{s3_prefix}")

        # Save detailed report
        report_filename = f"hls_upload_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        event_handler.upload_tracker.save_report(report_filename)

        # Generate presigned URLs for secure streaming
        print(f"\n🔗 GENERATING PRESIGNED URLS FOR SECURE STREAMING")
        print(f"{'='*60}")

        playlist_s3_uri = f"s3://{bucket_name}/{s3_prefix.rstrip('/')}/playlist.m3u8"

        try:
            # Create presigned HLS manager
            presigned_manager = S3PresignedHLSManager(region=dest_region, expiration_hours=24)

            # Generate presigned streaming manifest
            streaming_manifest = presigned_manager.create_streaming_manifest(
                s3_playlist_uri=playlist_s3_uri,
                output_dir="./presigned_hls"
            )

            if streaming_manifest:
                print(f"✅ Presigned URLs generated successfully!")
                print(f"🔗 Presigned Playlist URL:")
                print(f"   {streaming_manifest['presigned_playlist_url']}")
                print(f"📊 Streaming Info:")
                print(f"   • Total Segments: {streaming_manifest['segment_info']['total_segments']}")
                print(f"   • Duration: {streaming_manifest['segment_info']['playlist_duration']:.1f} seconds")
                print(f"   • Expires: {streaming_manifest['expires_at']}")
                print(f"📁 Local Files:")
                print(f"   • Presigned Playlist: ./presigned_hls/presigned_playlist.m3u8")
                print(f"   • Streaming Manifest: ./presigned_hls/streaming_manifest.json")

                # Save streaming info to the upload report
                streaming_info = {
                    'presigned_playlist_url': streaming_manifest['presigned_playlist_url'],
                    'total_segments': streaming_manifest['segment_info']['total_segments'],
                    'duration_seconds': streaming_manifest['segment_info']['playlist_duration'],
                    'expires_at': streaming_manifest['expires_at'],
                    'local_presigned_playlist': './presigned_hls/presigned_playlist.m3u8'
                }

                # Update the report with streaming info
                report = event_handler.upload_tracker.get_summary()
                report['streaming_info'] = streaming_info

                # Save updated report
                with open(report_filename, 'w') as f:
                    json.dump(report, f, indent=2)

                print(f"📄 Updated report with streaming info: {report_filename}")

            else:
                print(f"❌ Failed to generate presigned URLs")

        except Exception as e:
            print(f"⚠️  Error generating presigned URLs: {e}")
            print(f"💡 You can manually generate them later using:")
            print(f"   python3 s3_presigned_hls.py")

        print(f"{'='*80}\n")

    finally:
        # Cleanup temporary directory
        shutil.rmtree(temp_dir, ignore_errors=True)
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🧹 Cleaned up temporary directory")

if __name__ == "__main__":
    main()
