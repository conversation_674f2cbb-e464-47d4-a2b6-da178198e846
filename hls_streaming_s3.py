#!/usr/bin/env python3

"""
Real-time HLS streaming to S3 script
Converts video to HLS segments and uploads them to S3 in real-time using watchdog
"""

import os
import sys
import time
import subprocess
import boto3
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import tempfile
import shutil

class HLSUploadHandler(FileSystemEventHandler):
    """Handles file system events for HLS segment uploads"""
    
    def __init__(self, s3_client, bucket_name, s3_prefix, region):
        self.s3_client = s3_client
        self.bucket_name = bucket_name
        self.s3_prefix = s3_prefix
        self.region = region
        self.uploaded_files = set()
        
    def log_message(self, message):
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {message}")
        
    def upload_file_to_s3(self, local_path, s3_key):
        """Upload a single file to S3 using PUT"""
        try:
            file_size = os.path.getsize(local_path)
            
            # Upload file
            start_time = time.time()
            self.s3_client.upload_file(
                local_path,
                self.bucket_name,
                s3_key
            )
            upload_time = time.time() - start_time
            
            # Calculate throughput
            throughput_mbps = (file_size / 1024 / 1024) / max(upload_time, 0.001)
            file_size_kb = file_size / 1024
            
            self.log_message(f"🚀 UPLOADED: {os.path.basename(local_path)} → s3://{self.bucket_name}/{s3_key}")
            self.log_message(f"📊 Size: {file_size_kb:.1f}KB, Time: {upload_time:.2f}s, Speed: {throughput_mbps:.2f}MB/s")
            
            return True
            
        except Exception as e:
            self.log_message(f"❌ UPLOAD FAILED: {local_path} - {str(e)}")
            return False
    
    def on_created(self, event):
        """Handle file creation events"""
        if event.is_directory:
            return
            
        file_path = event.src_path
        filename = os.path.basename(file_path)
        
        # Only process .ts segments and .m3u8 playlists
        if not (filename.endswith('.ts') or filename.endswith('.m3u8')):
            return
            
        # Avoid duplicate uploads
        if file_path in self.uploaded_files:
            return
            
        # Wait a moment for file to be fully written
        time.sleep(0.1)
        
        # Check if file exists and has content
        if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
            return
            
        # Upload to S3
        s3_key = f"{self.s3_prefix.rstrip('/')}/{filename}" if self.s3_prefix else filename
        
        if self.upload_file_to_s3(file_path, s3_key):
            self.uploaded_files.add(file_path)
            
    def on_modified(self, event):
        """Handle file modification events (for playlist updates)"""
        if event.is_directory:
            return
            
        file_path = event.src_path
        filename = os.path.basename(file_path)
        
        # Only process .m3u8 playlist updates
        if not filename.endswith('.m3u8'):
            return
            
        # Wait a moment for file to be fully written
        time.sleep(0.1)
        
        # Check if file exists and has content
        if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
            return
            
        # Upload updated playlist to S3
        s3_key = f"{self.s3_prefix.rstrip('/')}/{filename}" if self.s3_prefix else filename
        
        self.log_message(f"📝 PLAYLIST UPDATED: {filename}")
        self.upload_file_to_s3(file_path, s3_key)

def parse_s3_path(s3_path):
    """Parse S3 path into bucket and key components"""
    if not s3_path.startswith('s3://'):
        raise ValueError("S3 path must start with s3://")
    
    path_parts = s3_path[5:].split('/', 1)
    bucket = path_parts[0]
    key = path_parts[1] if len(path_parts) > 1 else ""
    
    return bucket, key

def start_ffmpeg_hls(input_source, output_dir, source_region=None):
    """Start FFmpeg HLS encoding process"""
    
    # Prepare input command
    if input_source.startswith('s3://'):
        # S3 input - stream from S3
        input_cmd = [
            'aws', 's3', 'cp', input_source, '-'
        ]
        if source_region:
            input_cmd.extend(['--region', source_region])
        
        ffmpeg_input = 'pipe:0'
    else:
        # Local file input
        input_cmd = None
        ffmpeg_input = input_source
    
    # FFmpeg HLS command
    ffmpeg_cmd = [
        'ffmpeg',
        '-y',  # Overwrite output files
        '-hide_banner',
        '-loglevel', 'error',  # Quiet logging
        '-i', ffmpeg_input,
        '-c:v', 'libx264',     # Video codec
        '-crf', '28',          # Quality setting
        '-preset', 'superfast', # Encoding speed
        '-c:a', 'aac',         # Audio codec
        '-f', 'hls',           # HLS format
        '-hls_time', '2',      # 2-second segments
        '-hls_list_size', '0', # Keep all segments in playlist
        '-start_number', '0',  # Start numbering from 0
        '-hls_segment_filename', os.path.join(output_dir, 'stream_%04d.ts'),
        os.path.join(output_dir, 'playlist.m3u8')
    ]
    
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎬 Starting HLS encoding...")
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 📁 Output directory: {output_dir}")
    print(f"[{datetime.now().strftime('%H:%M:%S')}] ⚙️  HLS settings: 2s segments, unlimited playlist")
    
    # Start the process
    if input_cmd:
        # Pipe from S3 to FFmpeg
        s3_process = subprocess.Popen(input_cmd, stdout=subprocess.PIPE)
        ffmpeg_process = subprocess.Popen(ffmpeg_cmd, stdin=s3_process.stdout, 
                                        stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        s3_process.stdout.close()  # Allow s3_process to receive SIGPIPE
        return ffmpeg_process, s3_process
    else:
        # Direct file input
        ffmpeg_process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return ffmpeg_process, None

def main():
    if len(sys.argv) < 3:
        print("Usage: python3 hls_streaming_s3.py <input_source> <s3_output_path> [source_region] [dest_region]")
        print("")
        print("Examples:")
        print("  python3 hls_streaming_s3.py /path/to/video.y4m s3://bucket/output/stream/ us-east-1 us-west-1")
        print("  python3 hls_streaming_s3.py s3://bucket/input/video.y4m s3://bucket/output/stream/ us-east-1 us-west-1")
        sys.exit(1)
    
    input_source = sys.argv[1]
    s3_output_path = sys.argv[2]
    source_region = sys.argv[3] if len(sys.argv) > 3 else None
    dest_region = sys.argv[4] if len(sys.argv) > 4 else source_region
    
    # Parse S3 output path
    try:
        bucket_name, s3_prefix = parse_s3_path(s3_output_path)
    except ValueError as e:
        print(f"Error: {e}")
        sys.exit(1)
    
    # Create S3 client
    s3_client = boto3.client('s3', region_name=dest_region)
    
    # Create temporary directory for HLS output
    temp_dir = tempfile.mkdtemp(prefix='hls_streaming_')
    
    try:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎯 Starting real-time HLS streaming to S3")
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 📥 Input: {input_source}")
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 📤 Output: s3://{bucket_name}/{s3_prefix}")
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🗂️  Temp dir: {temp_dir}")
        
        # Set up file watcher
        event_handler = HLSUploadHandler(s3_client, bucket_name, s3_prefix, dest_region)
        observer = Observer()
        observer.schedule(event_handler, temp_dir, recursive=False)
        observer.start()
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 👁️  File watcher started")
        
        # Start FFmpeg HLS encoding
        ffmpeg_process, _ = start_ffmpeg_hls(input_source, temp_dir, source_region)

        # Wait for FFmpeg to complete
        _, stderr = ffmpeg_process.communicate()

        # Check results
        if ffmpeg_process.returncode == 0:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ✅ HLS encoding completed successfully")
        else:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ FFmpeg failed with return code {ffmpeg_process.returncode}")
            if stderr:
                print(f"Error: {stderr.decode()}")

        # Wait a moment for final uploads
        time.sleep(2)

        # Ensure playlist is uploaded (force upload if missed by watcher)
        playlist_path = os.path.join(temp_dir, 'playlist.m3u8')
        if os.path.exists(playlist_path):
            playlist_s3_key = f"{s3_prefix.rstrip('/')}/playlist.m3u8" if s3_prefix else "playlist.m3u8"
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 📝 Ensuring playlist upload: playlist.m3u8")
            event_handler.upload_file_to_s3(playlist_path, playlist_s3_key)
        
        # Stop file watcher
        observer.stop()
        observer.join()
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🎯 HLS streaming to S3 completed")
        
    finally:
        # Cleanup temporary directory
        shutil.rmtree(temp_dir, ignore_errors=True)
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 🧹 Cleaned up temporary directory")

if __name__ == "__main__":
    main()
