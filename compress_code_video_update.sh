#!/bin/bash

# Check if both input and output arguments are provided
if [ $# -ne 2 ] && [ $# -ne 4 ]; then
    echo "Usage: $0 <input_source> <output_destination> [source_region] [dest_region]"
    echo "Examples:"
    echo "  $0 /local/input/folder /local/output/folder"
    echo "  $0 s3://bucket/input/ /local/output/ us-east-1"
    echo "  $0 /local/input/ s3://bucket/output/ us-west-2"
    echo "  $0 s3://input-bucket/folder/ s3://output-bucket/folder/ us-east-1 us-west-2"
    exit 1
fi

INPUT_SOURCE="$1"
OUTPUT_DESTINATION="$2"
SOURCE_REGION="${3:-$AWS_REGION}"  # Default to AWS_REGION env var if not specified
DEST_REGION="${4:-$SOURCE_REGION}" # Default to SOURCE_REGION if not specified

echo "Starting real-time streaming video encoding..."
echo "Input source: $INPUT_SOURCE (Region: ${SOURCE_REGION:-default})"
echo "Output destination: $OUTPUT_DESTINATION (Region: ${DEST_REGION:-default})"

# Function to get list of files from S3 or local
get_file_list() {
    if [[ "$INPUT_SOURCE" == s3://* ]]; then
        # Remove trailing slash if present for consistent path handling
        local s3_path="${INPUT_SOURCE%/}"
        # Extract bucket and prefix
        local bucket_and_prefix="${s3_path#s3://}"
        local bucket="${bucket_and_prefix%%/*}"
        local prefix="${bucket_and_prefix#*/}"

        # List files and construct proper S3 URIs
        aws s3 ls "$s3_path/" --recursive --region "$SOURCE_REGION" | grep "\.y4m$" | awk '{print $4}' | while read -r file_path; do
            echo "s3://${bucket}/${file_path}"
        done
    else
        find "$INPUT_SOURCE" -name "*.y4m" -type f
    fi
}

# Function to stream encode from S3 input to S3 output
stream_s3_to_s3() {
    local input_file="$1"
    local output_file="$2"
    
    echo "Streaming: $input_file -> $output_file"
    
    # Create a temporary file for intermediate storage
    local temp_file="/tmp/$(basename "$input_file" .y4m)_temp.mp4"
    
    # Download, encode, and upload as separate steps
    if aws s3 cp "$input_file" - --region "$SOURCE_REGION" | ffmpeg -y -hide_banner -loglevel error -i pipe:0 -vcodec libx264 -crf 28 -preset superfast "$temp_file"; then
        aws s3 cp "$temp_file" "$output_file" --region "$DEST_REGION" && rm "$temp_file"
        return 0
    else
        rm -f "$temp_file"
        return 1
    fi
}

# Function to stream encode from S3 input to local output
stream_s3_to_local() {
    local input_file="$1"
    local output_file="$2"
    
    echo "Streaming: $input_file -> $output_file"
    mkdir -p "$(dirname "$output_file")"
    aws s3 cp "$input_file" - --region "$SOURCE_REGION" | ffmpeg -y -hide_banner -loglevel error -i pipe:0 -vcodec libx264 -crf 28 -preset superfast "$output_file"
}

# Function to stream encode from local input to S3 output
stream_local_to_s3() {
    local input_file="$1"
    local output_file="$2"
    
    echo "Streaming: $input_file -> $output_file"
    # Create a temporary file for intermediate storage
    local temp_file="/tmp/$(basename "$input_file" .y4m)_temp.mp4"
    
    # Encode to temp file then upload
    if ffmpeg -y -hide_banner -loglevel error -i "$input_file" -vcodec libx264 -crf 28 -preset superfast "$temp_file"; then
        aws s3 cp "$temp_file" "$output_file" --region "$DEST_REGION" && rm "$temp_file"
        return 0
    else
        rm -f "$temp_file"
        return 1
    fi
}

# Function to stream encode from local input to local output
stream_local_to_local() {
    local input_file="$1"
    local output_file="$2"
    
    echo "Streaming: $input_file -> $output_file"
    mkdir -p "$(dirname "$output_file")"
    ffmpeg -y -hide_banner -loglevel error -i "$input_file" -vcodec libx264 -crf 28 -preset superfast "$output_file"
}

# Function to handle original files after successful encoding
handle_originals() {
    local original_file="$1"

    # Keep original files in their original location - no moving
    echo "✓ Original file preserved: $original_file"

    # Optional: Add a comment or log entry about preservation
    if [[ "$INPUT_SOURCE" == s3://* ]]; then
        echo "  S3 original maintained at: $original_file"
    else
        echo "  Local original maintained at: $original_file"
    fi
}

# Main processing function
process_files() {
    local file_list
    file_list=$(get_file_list)
    
    if [ -z "$file_list" ]; then
        echo "No .y4m files found in input source"
        exit 1
    fi
    
    local file_count=0
    local success_count=0
    
    while IFS= read -r input_file; do
        [ -z "$input_file" ] && continue
        
        file_count=$((file_count + 1))
        base_name=$(basename "$input_file" .y4m)
        
        # Determine output path
        if [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
            # Remove trailing slash from output destination
            local output_dest="${OUTPUT_DESTINATION%/}"
            output_file="${output_dest}/${base_name}_zmt.mp4"
        else
            output_file="${OUTPUT_DESTINATION}/${base_name}_zmt.mp4"
        fi
        
        echo "Processing file $file_count: $(basename "$input_file")"
        
        # Choose streaming method based on input/output types
        if [[ "$INPUT_SOURCE" == s3://* ]] && [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
            stream_s3_to_s3 "$input_file" "$output_file"
        elif [[ "$INPUT_SOURCE" == s3://* ]]; then
            stream_s3_to_local "$input_file" "$output_file"
        elif [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
            stream_local_to_s3 "$input_file" "$output_file"
        else
            stream_local_to_local "$input_file" "$output_file"
        fi
        
        # Check if encoding was successful
        if [ $? -eq 0 ]; then
            echo "✓ Successfully streamed: ${base_name}_zmt.mp4"
            handle_originals "$input_file"
            success_count=$((success_count + 1))
        else
            echo "✗ Error streaming: $(basename "$input_file")"
        fi
        
    done <<< "$file_list"
    
    echo "Finished! Processed $success_count/$file_count files successfully."
}

# Validate dependencies
check_dependencies() {
    if ! command -v ffmpeg &> /dev/null; then
        echo "Error: ffmpeg is not installed"
        exit 1
    fi
    
    if [[ "$INPUT_SOURCE" == s3://* ]] || [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
        if ! command -v aws &> /dev/null; then
            echo "Error: AWS CLI is not installed"
            exit 1
        fi
    fi
}

# Main execution
check_dependencies
process_files
