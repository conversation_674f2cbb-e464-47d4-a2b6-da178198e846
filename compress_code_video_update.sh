#!/bin/bash

# Check if both input and output arguments are provided
if [ $# -ne 2 ] && [ $# -ne 4 ]; then
    echo "Usage: $0 <input_source> <output_destination> [source_region] [dest_region]"
    echo "Examples:"
    echo "  $0 /local/input/folder /local/output/folder"
    echo "  $0 s3://bucket/input/ /local/output/ us-east-1"
    echo "  $0 /local/input/ s3://bucket/output/ us-west-2"
    echo "  $0 s3://input-bucket/folder/ s3://output-bucket/folder/ us-east-1 us-west-2"
    exit 1
fi

INPUT_SOURCE="$1"
OUTPUT_DESTINATION="$2"
SOURCE_REGION="${3:-$AWS_REGION}"  # Default to AWS_REGION env var if not specified
DEST_REGION="${4:-$SOURCE_REGION}" # Default to SOURCE_REGION if not specified

echo "Starting real-time streaming video encoding..."
echo "Input source: $INPUT_SOURCE (Region: ${SOURCE_REGION:-default})"
echo "Output destination: $OUTPUT_DESTINATION (Region: ${DEST_REGION:-default})"

# Function to get list of files from S3 or local
get_file_list() {
    if [[ "$INPUT_SOURCE" == s3://* ]]; then
        # Remove trailing slash if present for consistent path handling
        local s3_path="${INPUT_SOURCE%/}"
        # Extract bucket and prefix
        local bucket_and_prefix="${s3_path#s3://}"
        local bucket="${bucket_and_prefix%%/*}"
        local prefix="${bucket_and_prefix#*/}"

        # List files and construct proper S3 URIs
        aws s3 ls "$s3_path/" --recursive --region "$SOURCE_REGION" | grep "\.y4m$" | awk '{print $4}' | while read -r file_path; do
            echo "s3://${bucket}/${file_path}"
        done
    else
        find "$INPUT_SOURCE" -name "*.y4m" -type f
    fi
}

# Function to stream encode from S3 input to S3 output with TRUE real-time streaming
stream_s3_to_s3() {
    local input_file="$1"
    local output_file="$2"

    echo "Streaming: $input_file -> $output_file"
    echo "Starting TRUE real-time S3-to-S3 streaming compression..."

    # Extract bucket and key from output S3 URI
    local output_bucket="${output_file#s3://}"
    output_bucket="${output_bucket%%/*}"
    local output_key="${output_file#s3://$output_bucket/}"

    echo "Output bucket: $output_bucket"
    echo "Output key: $output_key"

    # Create a temporary file for chunked streaming
    local temp_file="/tmp/$(basename "$input_file" .y4m)_streaming.mp4"

    echo "Starting multipart upload for real-time streaming..."

    # Start multipart upload
    local upload_id=$(aws s3api create-multipart-upload \
        --bucket "$output_bucket" \
        --key "$output_key" \
        --region "$DEST_REGION" \
        --query 'UploadId' --output text)

    if [ -z "$upload_id" ]; then
        echo "✗ Failed to create multipart upload"
        return 1
    fi

    echo "Multipart upload started with ID: $upload_id"

    # Create temporary file for real-time monitoring
    local temp_file="/tmp/$(basename "$input_file" .y4m)_streaming.mp4"

    echo "Starting TRUE real-time streaming with file monitoring..."

    # Configuration for streaming
    local CHUNK_SIZE_MB=1  # 1MB buffer increments
    local UPLOAD_SIZE_MB=5  # 5MB upload parts
    local BUFFER_SIZE=$((CHUNK_SIZE_MB * 1024 * 1024))
    local UPLOAD_SIZE=$((UPLOAD_SIZE_MB * 1024 * 1024))

    # Start background process to monitor file growth and stream to S3
    (
        local timestamp=$(date '+%H:%M:%S')
        echo "[$timestamp] 🎬 Starting real-time S3 streaming monitor"
        echo "[$timestamp] ⚙️  Config: Buffer=${CHUNK_SIZE_MB}MB, Upload=${UPLOAD_SIZE_MB}MB, Target: $temp_file"
        echo "[$timestamp] 🎯 Destination: s3://$output_bucket/$output_key"

        # Wait for file to be created
        while [ ! -f "$temp_file" ]; do
            sleep 0.1
        done
        echo "Target file created: $temp_file"

        # Initialize streaming variables
        local part_number=1
        local uploaded_parts=""
        local file_position=0  # Current read position in file
        local buffer_file="/tmp/stream_buffer_$$.dat"
        local buffer_size=0    # Current buffer size
        local chunk_file="/tmp/chunk_part_$$.dat"
        local encoding_finished=false
        local consecutive_no_growth=0
        local max_no_growth=10

        # Create empty buffer
        > "$buffer_file"

        echo "Real-time streaming monitor started..."

        # Main streaming loop
        while true; do
            if [ ! -f "$temp_file" ]; then
                echo "Source file disappeared - marking encoding as finished"
                encoding_finished=true
            fi

            # Get current file size
            local current_file_size=0
            if [ -f "$temp_file" ]; then
                current_file_size=$(stat -f%z "$temp_file" 2>/dev/null || stat -c%s "$temp_file" 2>/dev/null || echo 0)
            fi

            local bytes_available=$((current_file_size - file_position))

            # Check for file growth
            if [ $bytes_available -gt 0 ]; then
                consecutive_no_growth=0
                local timestamp=$(date '+%H:%M:%S')
                echo "[$timestamp] 📈 New data: +${bytes_available} bytes (file: ${current_file_size} bytes, position: ${file_position})"

                # Read new data in CHUNK_SIZE_MB increments
                while [ $bytes_available -ge $BUFFER_SIZE ]; do
                    local timestamp=$(date '+%H:%M:%S')
                    echo "[$timestamp] 📖 Reading ${CHUNK_SIZE_MB}MB chunk from position $file_position"

                    # Extract chunk and append to buffer using byte-level skip
                    dd if="$temp_file" bs=1 skip=$file_position count=$BUFFER_SIZE 2>/dev/null >> "$buffer_file"

                    file_position=$((file_position + BUFFER_SIZE))
                    buffer_size=$((buffer_size + BUFFER_SIZE))
                    bytes_available=$((bytes_available - BUFFER_SIZE))

                    local buffer_mb=$(($buffer_size / 1024 / 1024))
                    echo "[$timestamp] 🗂️  Buffer: ${buffer_size} bytes (${buffer_mb}MB)"

                    # Check if buffer is ready for upload
                    if [ $buffer_size -ge $UPLOAD_SIZE ]; then
                        local timestamp=$(date '+%H:%M:%S')
                        local upload_mb=$(($UPLOAD_SIZE / 1024 / 1024))
                        local part_start_pos=$((file_position - buffer_size))
                        local part_end_pos=$((part_start_pos + UPLOAD_SIZE - 1))
                        echo "[$timestamp] 🚀 UPLOADING Part $part_number: ${UPLOAD_SIZE} bytes (${upload_mb}MB) to S3"
                        echo "[$timestamp] 📍 Byte range: ${part_start_pos} → ${part_end_pos}"

                        # Extract exact part data directly from temp file
                        dd if="$temp_file" of="$chunk_file" bs=1 skip=$part_start_pos count=$UPLOAD_SIZE 2>/dev/null

                        local upload_start=$(date +%s)
                        local etag=$(aws s3api upload-part \
                            --bucket "$output_bucket" \
                            --key "$output_key" \
                            --part-number $part_number \
                            --upload-id "$upload_id" \
                            --body "$chunk_file" \
                            --region "$DEST_REGION" \
                            --query 'ETag' --output text)
                        local upload_end=$(date +%s)
                        local upload_duration=$((upload_end - upload_start))

                        if [ -n "$etag" ]; then
                            uploaded_parts="${uploaded_parts}{\"ETag\":$etag,\"PartNumber\":$part_number},"
                            local timestamp=$(date '+%H:%M:%S')
                            local throughput_mbps=$(($UPLOAD_SIZE / 1024 / 1024 / (upload_duration + 1)))
                            echo "[$timestamp] ✅ Part $part_number SUCCESS: ${upload_mb}MB in ${upload_duration}s (~${throughput_mbps}MB/s)"
                            echo "[$timestamp] 🏷️  ETag: $etag"
                            part_number=$((part_number + 1))

                            # Reset buffer - adjust positions
                            buffer_size=$((buffer_size - UPLOAD_SIZE))
                            # Move remaining buffer data to start of buffer
                            if [ $buffer_size -gt 0 ]; then
                                local remaining_start=$((part_start_pos + UPLOAD_SIZE))
                                dd if="$temp_file" of="$buffer_file" bs=1 skip=$remaining_start count=$buffer_size 2>/dev/null
                            else
                                > "$buffer_file"
                            fi
                        else
                            local timestamp=$(date '+%H:%M:%S')
                            echo "[$timestamp] ❌ Part $part_number FAILED"
                            break
                        fi

                        rm -f "$chunk_file"
                    fi
                done

                # Handle remaining bytes smaller than BUFFER_SIZE
                if [ $bytes_available -gt 0 ]; then
                    local timestamp=$(date '+%H:%M:%S')
                    echo "[$timestamp] 📖 Reading remaining ${bytes_available} bytes from position $file_position"
                    dd if="$temp_file" bs=1 skip=$file_position count=$bytes_available 2>/dev/null >> "$buffer_file"
                    file_position=$((file_position + bytes_available))
                    buffer_size=$((buffer_size + bytes_available))
                    local buffer_kb=$(($buffer_size / 1024))
                    echo "[$timestamp] 🗂️  Buffer: ${buffer_size} bytes (${buffer_kb}KB)"
                fi

            else
                # No new data available
                consecutive_no_growth=$((consecutive_no_growth + 1))
                local timestamp=$(date '+%H:%M:%S')
                echo "[$timestamp] ⏳ Waiting... no new data for ${consecutive_no_growth}s (file: ${current_file_size} bytes)"

                if [ $consecutive_no_growth -ge $max_no_growth ]; then
                    local timestamp=$(date '+%H:%M:%S')
                    echo "[$timestamp] 🏁 Encoding finished - no growth for ${max_no_growth}s"
                    encoding_finished=true
                fi
            fi

            # Check if we should finish
            if [ "$encoding_finished" = true ]; then
                local timestamp=$(date '+%H:%M:%S')
                echo "[$timestamp] 🎬 Encoding complete - uploading final buffer if needed"

                # Upload any remaining buffer data
                if [ $buffer_size -gt 0 ]; then
                    local final_kb=$(($buffer_size / 1024))
                    local timestamp=$(date '+%H:%M:%S')
                    echo "[$timestamp] 🚀 UPLOADING Final Part $part_number: ${buffer_size} bytes (${final_kb}KB)"
                    echo "[$timestamp] 📍 Final byte range: $((file_position - buffer_size)) → $((file_position - 1))"

                    cp "$buffer_file" "$chunk_file"

                    local upload_start=$(date +%s)
                    local final_etag=$(aws s3api upload-part \
                        --bucket "$output_bucket" \
                        --key "$output_key" \
                        --part-number $part_number \
                        --upload-id "$upload_id" \
                        --body "$chunk_file" \
                        --region "$DEST_REGION" \
                        --query 'ETag' --output text)
                    local upload_end=$(date +%s)
                    local upload_duration=$((upload_end - upload_start))

                    if [ -n "$final_etag" ]; then
                        uploaded_parts="${uploaded_parts}{\"ETag\":$final_etag,\"PartNumber\":$part_number}"
                        local timestamp=$(date '+%H:%M:%S')
                        echo "[$timestamp] ✅ Final Part $part_number SUCCESS: ${final_kb}KB in ${upload_duration}s"
                        echo "[$timestamp] 🏷️  Final ETag: $final_etag"
                        part_number=$((part_number + 1))
                    else
                        local timestamp=$(date '+%H:%M:%S')
                        echo "[$timestamp] ❌ Final Part $part_number FAILED"
                    fi

                    rm -f "$chunk_file"
                fi

                local timestamp=$(date '+%H:%M:%S')
                local total_mb=$(($file_position / 1024 / 1024))
                echo "[$timestamp] 🎯 STREAMING COMPLETE: ${file_position} bytes (${total_mb}MB) → $((part_number - 1)) parts uploaded"
                break
            fi

            sleep 1  # Check every second
        done

        # Cleanup
        rm -f "$buffer_file" "$chunk_file"

        # Save uploaded parts for completion
        echo "$uploaded_parts" > "/tmp/uploaded_parts_$$"
        echo "$part_number" > "/tmp/part_number_$$"

        echo "Real-time streaming monitor finished with $((part_number - 1)) parts"
    ) &

    local uploader_pid=$!

    # Start encoding to temp file (quiet FFmpeg, focus on streaming logs)
    echo "Starting encoding with real-time file monitoring..."
    aws s3 cp "$input_file" - --region "$SOURCE_REGION" | \
    ffmpeg -y -hide_banner -loglevel error \
    -i pipe:0 -vcodec libx264 -crf 28 -preset superfast \
    -f mp4 -movflags +faststart "$temp_file" &

    local ffmpeg_pid=$!

    # Wait for encoding to complete
    wait $ffmpeg_pid
    local ffmpeg_result=$?

    # Wait for uploader to finish
    wait $uploader_pid

    # Wait for encoding to complete
    wait $ffmpeg_pid
    local ffmpeg_result=$?

    # Wait for background streaming process to complete
    echo "Waiting for streaming monitor to complete..."
    wait $uploader_pid
    local uploader_result=$?
    echo "Streaming monitor completed with result: $uploader_result"

    # Read uploaded parts info
    local uploaded_parts=""
    local part_number=1
    if [ -f "/tmp/uploaded_parts_$$" ]; then
        uploaded_parts=$(cat "/tmp/uploaded_parts_$$")
        part_number=$(cat "/tmp/part_number_$$")
        rm -f "/tmp/uploaded_parts_$$" "/tmp/part_number_$$"
        echo "Retrieved multipart upload data: $((part_number - 1)) parts"
    else
        echo "Warning: No multipart upload data found"
    fi

    if [ $ffmpeg_result -eq 0 ]; then
        echo "✓ Encoding completed successfully"

        # Complete multipart upload with real-time uploaded parts
        if [ -n "$uploaded_parts" ]; then
            uploaded_parts="[${uploaded_parts%,}]"
            echo "Completing multipart upload with $((part_number - 1)) real-time parts"

            aws s3api complete-multipart-upload \
                --bucket "$output_bucket" \
                --key "$output_key" \
                --upload-id "$upload_id" \
                --multipart-upload "{\"Parts\":$uploaded_parts}" \
                --region "$DEST_REGION"

            local complete_result=$?

            if [ $complete_result -eq 0 ]; then
                echo "✓ TRUE real-time streaming completed successfully"
                rm -f "$temp_file"
                return 0
            else
                echo "✗ Failed to complete multipart upload"
                rm -f "$temp_file"
                return 1
            fi
        else
            echo "✗ No parts were uploaded"
            aws s3api abort-multipart-upload \
                --bucket "$output_bucket" \
                --key "$output_key" \
                --upload-id "$upload_id" \
                --region "$DEST_REGION"
            rm -f "$temp_file"
            return 1
        fi
    else
        echo "✗ Encoding failed"

        # Abort multipart upload
        aws s3api abort-multipart-upload \
            --bucket "$output_bucket" \
            --key "$output_key" \
            --upload-id "$upload_id" \
            --region "$DEST_REGION"

        rm -f "$temp_file"
        return 1
    fi
}

# Function to stream encode from S3 input to local output
stream_s3_to_local() {
    local input_file="$1"
    local output_file="$2"

    echo "Streaming: $input_file -> $output_file"
    echo "Starting real-time S3-to-local streaming compression..."
    mkdir -p "$(dirname "$output_file")"

    # Stream with progress logging
    aws s3 cp "$input_file" - --region "$SOURCE_REGION" | \
    ffmpeg -y -hide_banner -loglevel info -progress pipe:2 \
    -i pipe:0 -vcodec libx264 -crf 28 -preset superfast "$output_file"
}

# Function to stream encode from local input to S3 output
stream_local_to_s3() {
    local input_file="$1"
    local output_file="$2"

    echo "Streaming: $input_file -> $output_file"
    echo "Starting real-time local-to-S3 streaming compression..."

    # Use direct pipe streaming: Local file -> FFmpeg -> S3 upload
    echo "Starting streaming pipeline: Local -> FFmpeg -> S3..."

    # Stream encode -> upload (direct pipeline)
    # Use streaming-friendly MP4 format with proper flags for pipe output
    ffmpeg -y -hide_banner -loglevel info -progress pipe:2 \
    -i "$input_file" -vcodec libx264 -crf 28 -preset superfast \
    -f mp4 -movflags +frag_keyframe+empty_moov+default_base_moof pipe:1 | \
    aws s3 cp - "$output_file" --region "$DEST_REGION"

    local result=$?

    if [ $result -eq 0 ]; then
        echo "✓ Real-time streaming completed successfully"
        return 0
    else
        echo "✗ Streaming pipeline failed"
        return 1
    fi
}

# Function to stream encode from local input to local output
stream_local_to_local() {
    local input_file="$1"
    local output_file="$2"

    echo "Streaming: $input_file -> $output_file"
    echo "Starting real-time local-to-local streaming compression..."
    mkdir -p "$(dirname "$output_file")"

    # Stream with progress logging
    ffmpeg -y -hide_banner -loglevel info -progress pipe:2 \
    -i "$input_file" -vcodec libx264 -crf 28 -preset superfast "$output_file"
}

# Function to handle original files after successful encoding
handle_originals() {
    local original_file="$1"

    # Keep original files in their original location - no moving
    echo "✓ Original file preserved: $original_file"

    # Optional: Add a comment or log entry about preservation
    if [[ "$INPUT_SOURCE" == s3://* ]]; then
        echo "  S3 original maintained at: $original_file"
    else
        echo "  Local original maintained at: $original_file"
    fi
}

# Main processing function
process_files() {
    local file_list
    file_list=$(get_file_list)
    
    if [ -z "$file_list" ]; then
        echo "No .y4m files found in input source"
        exit 1
    fi
    
    local file_count=0
    local success_count=0
    
    while IFS= read -r input_file; do
        [ -z "$input_file" ] && continue
        
        file_count=$((file_count + 1))
        base_name=$(basename "$input_file" .y4m)
        
        # Determine output path
        if [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
            # Remove trailing slash from output destination
            local output_dest="${OUTPUT_DESTINATION%/}"
            output_file="${output_dest}/${base_name}_zmt.mp4"
        else
            output_file="${OUTPUT_DESTINATION}/${base_name}_zmt.mp4"
        fi
        
        echo "Processing file $file_count: $(basename "$input_file")"
        
        # Choose streaming method based on input/output types
        if [[ "$INPUT_SOURCE" == s3://* ]] && [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
            stream_s3_to_s3 "$input_file" "$output_file"
        elif [[ "$INPUT_SOURCE" == s3://* ]]; then
            stream_s3_to_local "$input_file" "$output_file"
        elif [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
            stream_local_to_s3 "$input_file" "$output_file"
        else
            stream_local_to_local "$input_file" "$output_file"
        fi
        
        # Check if encoding was successful
        if [ $? -eq 0 ]; then
            echo "✓ Successfully streamed: ${base_name}_zmt.mp4"
            handle_originals "$input_file"
            success_count=$((success_count + 1))
        else
            echo "✗ Error streaming: $(basename "$input_file")"
        fi
        
    done <<< "$file_list"
    
    echo "Finished! Processed $success_count/$file_count files successfully."
}

# Validate dependencies
check_dependencies() {
    if ! command -v ffmpeg &> /dev/null; then
        echo "Error: ffmpeg is not installed"
        exit 1
    fi
    
    if [[ "$INPUT_SOURCE" == s3://* ]] || [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
        if ! command -v aws &> /dev/null; then
            echo "Error: AWS CLI is not installed"
            exit 1
        fi
    fi
}

# Main execution
check_dependencies
process_files
