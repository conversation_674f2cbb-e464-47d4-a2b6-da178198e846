#!/bin/bash

# Check if both input and output arguments are provided
if [ $# -ne 2 ]; then
    echo "Usage: $0 <input_source> <output_destination>"
    echo "Examples:"
    echo "  $0 /local/input/folder /local/output/folder"
    echo "  $0 s3://bucket/input/ /local/output/"
    echo "  $0 /local/input/ s3://bucket/output/"
    echo "  $0 s3://input-bucket/folder/ s3://output-bucket/folder/"
    exit 1
fi

INPUT_SOURCE="$1"
OUTPUT_DESTINATION="$2"

echo "Starting video encoding process..."
echo "Input source: $INPUT_SOURCE"
echo "Output destination: $OUTPUT_DESTINATION"

# Function to download from S3 or use local files
get_input_files() {
    if [[ "$INPUT_SOURCE" == s3://* ]]; then
        echo "Downloading files from S3..."
        mkdir -p temp_input
        aws s3 sync "$INPUT_SOURCE" temp_input/ --exclude "*" --include "*.y4m"
        INPUT_DIR="temp_input"
    else
        echo "Using local input directory..."
        INPUT_DIR="$INPUT_SOURCE"
        if [ ! -d "$INPUT_DIR" ]; then
            echo "Error: Input directory $INPUT_DIR does not exist"
            exit 1
        fi
    fi
}

# Function to upload to S3 or move to local destination  
handle_output() {
    local encoded_file="$1"
    local base_name="$2"
    
    if [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
        echo "Uploading $encoded_file to S3..."
        aws s3 cp "$encoded_file" "${OUTPUT_DESTINATION}${base_name}_zmt.mp4"
        rm "$encoded_file"
    else
        echo "Moving $encoded_file to local destination..."
        mkdir -p "$OUTPUT_DESTINATION"
        mv "$encoded_file" "$OUTPUT_DESTINATION/"
    fi
}

# Function to handle original files
handle_originals() {
    local original_file="$1"
    
    if [[ "$INPUT_SOURCE" == s3://* ]]; then
        # If input was S3, just remove the temp downloaded file
        rm "$original_file"
    else
        # If input was local, move to original folder
        mkdir -p original
        mv "$original_file" "original/"
    fi
}

# Main processing
get_input_files

echo "Processing video files..."
file_count=0
for f in "$INPUT_DIR"/*.y4m; do
    if [ -f "$f" ]; then
        file_count=$((file_count + 1))
        base_name=$(basename "$f" .y4m)
        temp_encoded="temp_${base_name}_zmt.mp4"
        
        echo "Processing file $file_count: $base_name.y4m"
        
        # Encode the video
        ffmpeg -y -hide_banner -loglevel error -i "$f" -vcodec libx264 -crf 28 -preset superfast "$temp_encoded"
        
        if [ $? -eq 0 ]; then
            # Handle output (S3 upload or local move)
            handle_output "$temp_encoded" "$base_name"
            
            # Handle original file
            handle_originals "$f"
            
            echo "✓ Completed: ${base_name}_zmt.mp4"
        else
            echo "✗ Error processing: $f"
            rm -f "$temp_encoded"
        fi
    fi
done

# Cleanup temp directories
if [[ "$INPUT_SOURCE" == s3://* ]]; then
    rm -rf temp_input
fi

if [ $file_count -eq 0 ]; then
    echo "No .y4m files found in input source"
    exit 1
else
    echo "Finished! Processed $file_count files."
fi