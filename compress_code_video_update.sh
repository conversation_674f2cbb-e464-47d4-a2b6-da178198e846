#!/bin/bash

# Check if destination argument is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <destination>"
    echo "Examples:"
    echo "  $0 /path/to/local/folder"
    echo "  $0 s3://bucket-name/folder/"
    exit 1
fi

DESTINATION="$1"

echo "Compressing files please wait..."
echo "Destination: $DESTINATION"

# Check if destination is S3 or local path
if [[ "$DESTINATION" == s3://* ]]; then
    # S3 destination
    echo "Using S3 destination..."
    for f in *.y4m; do
        if [ -f "$f" ]; then
            echo "Processing: $f"
            # Encode to temporary local file first
            temp_file="temp_${f%.*}_zmt.mp4"
            ffmpeg -y -hide_banner -loglevel error -i "$f" -vcodec libx264 -crf 28 -preset superfast "$temp_file"
            
            # Upload to S3 and remove temp file
            aws s3 cp "$temp_file" "${DESTINATION}${f%.*}_zmt.mp4"
            rm "$temp_file"
            
            # Move original to local original folder
            mkdir -p original
            mv "$f" "original/"
        fi
    done
else
    # Local destination
    echo "Using local destination..."
    # Create destination directory if it doesn't exist
    mkdir -p "$DESTINATION"
    mkdir -p original
    
    for f in *.y4m; do
        if [ -f "$f" ]; then
            echo "Processing: $f"
            ffmpeg -y -hide_banner -loglevel error -i "$f" -vcodec libx264 -crf 28 -preset superfast "${DESTINATION}/${f%.*}_zmt.mp4"
            mv "$f" "original/"
        fi
    done
fi

echo "Finished!"
