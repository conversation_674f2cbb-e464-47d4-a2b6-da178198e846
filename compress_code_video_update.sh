#!/bin/bash

# Check if both input and output arguments are provided
if [ $# -ne 2 ] && [ $# -ne 4 ]; then
    echo "Usage: $0 <input_source> <output_destination> [source_region] [dest_region]"
    echo "Examples:"
    echo "  $0 /local/input/folder /local/output/folder"
    echo "  $0 s3://bucket/input/ /local/output/ us-east-1"
    echo "  $0 /local/input/ s3://bucket/output/ us-west-2"
    echo "  $0 s3://input-bucket/folder/ s3://output-bucket/folder/ us-east-1 us-west-2"
    exit 1
fi

INPUT_SOURCE="$1"
OUTPUT_DESTINATION="$2"
SOURCE_REGION="${3:-$AWS_REGION}"  # Default to AWS_REGION env var if not specified
DEST_REGION="${4:-$SOURCE_REGION}" # Default to SOURCE_REGION if not specified

echo "Starting real-time streaming video encoding..."
echo "Input source: $INPUT_SOURCE (Region: ${SOURCE_REGION:-default})"
echo "Output destination: $OUTPUT_DESTINATION (Region: ${DEST_REGION:-default})"

# Function to get list of files from S3 or local
get_file_list() {
    if [[ "$INPUT_SOURCE" == s3://* ]]; then
        # Remove trailing slash if present for consistent path handling
        local s3_path="${INPUT_SOURCE%/}"
        # Extract bucket and prefix
        local bucket_and_prefix="${s3_path#s3://}"
        local bucket="${bucket_and_prefix%%/*}"
        local prefix="${bucket_and_prefix#*/}"

        # List files and construct proper S3 URIs
        aws s3 ls "$s3_path/" --recursive --region "$SOURCE_REGION" | grep "\.y4m$" | awk '{print $4}' | while read -r file_path; do
            echo "s3://${bucket}/${file_path}"
        done
    else
        find "$INPUT_SOURCE" -name "*.y4m" -type f
    fi
}

# Function to stream encode from S3 input to S3 output with TRUE real-time streaming
stream_s3_to_s3() {
    local input_file="$1"
    local output_file="$2"

    echo "Streaming: $input_file -> $output_file"
    echo "Starting TRUE real-time S3-to-S3 streaming compression..."

    # Extract bucket and key from output S3 URI
    local output_bucket="${output_file#s3://}"
    output_bucket="${output_bucket%%/*}"
    local output_key="${output_file#s3://$output_bucket/}"

    echo "Output bucket: $output_bucket"
    echo "Output key: $output_key"

    # Create a temporary file for chunked streaming
    local temp_file="/tmp/$(basename "$input_file" .y4m)_streaming.mp4"

    echo "Starting multipart upload for real-time streaming..."

    # Start multipart upload
    local upload_id=$(aws s3api create-multipart-upload \
        --bucket "$output_bucket" \
        --key "$output_key" \
        --region "$DEST_REGION" \
        --query 'UploadId' --output text)

    if [ -z "$upload_id" ]; then
        echo "✗ Failed to create multipart upload"
        return 1
    fi

    echo "Multipart upload started with ID: $upload_id"

    # Create named pipe for true streaming
    local stream_pipe="/tmp/$(basename "$input_file" .y4m)_stream_pipe"
    mkfifo "$stream_pipe"

    echo "Starting TRUE real-time streaming pipeline..."

    # Start background process to read from pipe and upload chunks
    (
        local part_number=1
        local uploaded_parts=""
        local chunk_size=$((5 * 1024 * 1024))  # 5MB chunks for faster streaming
        local chunk_file="/tmp/chunk_buffer.mp4"

        echo "Real-time chunk uploader started..."

        # Read from pipe in chunks and upload immediately
        while true; do
            # Read chunk from pipe
            if dd if="$stream_pipe" of="$chunk_file" bs=$chunk_size count=1 2>/dev/null; then
                local chunk_actual_size=$(stat -f%z "$chunk_file" 2>/dev/null || stat -c%s "$chunk_file" 2>/dev/null || echo 0)

                if [ $chunk_actual_size -gt 0 ]; then
                    echo "Uploading real-time chunk $part_number (size: $chunk_actual_size bytes)"

                    # Upload part immediately
                    local etag=$(aws s3api upload-part \
                        --bucket "$output_bucket" \
                        --key "$output_key" \
                        --part-number $part_number \
                        --upload-id "$upload_id" \
                        --body "$chunk_file" \
                        --region "$DEST_REGION" \
                        --query 'ETag' --output text)

                    if [ -n "$etag" ]; then
                        uploaded_parts="${uploaded_parts}{\"ETag\":$etag,\"PartNumber\":$part_number},"
                        echo "✓ Uploaded real-time chunk $part_number with ETag: $etag"
                        part_number=$((part_number + 1))
                    else
                        echo "✗ Failed to upload chunk $part_number"
                    fi
                else
                    # No more data, pipe closed
                    break
                fi
            else
                # Error reading or pipe closed
                break
            fi
        done

        # Save uploaded parts for completion
        echo "$uploaded_parts" > "/tmp/uploaded_parts_$$"
        echo "$part_number" > "/tmp/part_number_$$"

        rm -f "$chunk_file"
        echo "Real-time chunk uploader finished"
    ) &

    local uploader_pid=$!

    # Start encoding and stream directly to pipe
    echo "Starting encoding with real-time streaming..."
    aws s3 cp "$input_file" - --region "$SOURCE_REGION" | \
    ffmpeg -y -hide_banner -loglevel info -progress pipe:2 \
    -i pipe:0 -vcodec libx264 -crf 28 -preset superfast \
    -f mp4 -movflags +frag_keyframe+empty_moov+default_base_moof "$stream_pipe"

    local ffmpeg_result=$?

    # Wait for uploader to finish
    wait $uploader_pid

    # Read uploaded parts info
    local uploaded_parts=""
    local part_number=1
    if [ -f "/tmp/uploaded_parts_$$" ]; then
        uploaded_parts=$(cat "/tmp/uploaded_parts_$$")
        part_number=$(cat "/tmp/part_number_$$")
        rm -f "/tmp/uploaded_parts_$$" "/tmp/part_number_$$"
    fi

    # Wait for encoding to complete
    wait $ffmpeg_pid
    local ffmpeg_result=$?

    if [ $ffmpeg_result -eq 0 ]; then
        echo "✓ Encoding completed successfully"

        # Complete multipart upload with real-time uploaded parts
        if [ -n "$uploaded_parts" ]; then
            uploaded_parts="[${uploaded_parts%,}]"
            echo "Completing multipart upload with $((part_number - 1)) real-time parts"

            aws s3api complete-multipart-upload \
                --bucket "$output_bucket" \
                --key "$output_key" \
                --upload-id "$upload_id" \
                --multipart-upload "{\"Parts\":$uploaded_parts}" \
                --region "$DEST_REGION"

            local complete_result=$?

            if [ $complete_result -eq 0 ]; then
                echo "✓ TRUE real-time streaming completed successfully"
                rm -f "$stream_pipe"
                return 0
            else
                echo "✗ Failed to complete multipart upload"
                rm -f "$stream_pipe"
                return 1
            fi
        else
            echo "✗ No parts were uploaded"
            aws s3api abort-multipart-upload \
                --bucket "$output_bucket" \
                --key "$output_key" \
                --upload-id "$upload_id" \
                --region "$DEST_REGION"
            rm -f "$stream_pipe"
            return 1
        fi
    else
        echo "✗ Encoding failed"

        # Abort multipart upload
        aws s3api abort-multipart-upload \
            --bucket "$output_bucket" \
            --key "$output_key" \
            --upload-id "$upload_id" \
            --region "$DEST_REGION"

        rm -f "$stream_pipe"
        return 1
    fi
}

# Function to stream encode from S3 input to local output
stream_s3_to_local() {
    local input_file="$1"
    local output_file="$2"

    echo "Streaming: $input_file -> $output_file"
    echo "Starting real-time S3-to-local streaming compression..."
    mkdir -p "$(dirname "$output_file")"

    # Stream with progress logging
    aws s3 cp "$input_file" - --region "$SOURCE_REGION" | \
    ffmpeg -y -hide_banner -loglevel info -progress pipe:2 \
    -i pipe:0 -vcodec libx264 -crf 28 -preset superfast "$output_file"
}

# Function to stream encode from local input to S3 output
stream_local_to_s3() {
    local input_file="$1"
    local output_file="$2"

    echo "Streaming: $input_file -> $output_file"
    echo "Starting real-time local-to-S3 streaming compression..."

    # Use direct pipe streaming: Local file -> FFmpeg -> S3 upload
    echo "Starting streaming pipeline: Local -> FFmpeg -> S3..."

    # Stream encode -> upload (direct pipeline)
    # Use streaming-friendly MP4 format with proper flags for pipe output
    ffmpeg -y -hide_banner -loglevel info -progress pipe:2 \
    -i "$input_file" -vcodec libx264 -crf 28 -preset superfast \
    -f mp4 -movflags +frag_keyframe+empty_moov+default_base_moof pipe:1 | \
    aws s3 cp - "$output_file" --region "$DEST_REGION"

    local result=$?

    if [ $result -eq 0 ]; then
        echo "✓ Real-time streaming completed successfully"
        return 0
    else
        echo "✗ Streaming pipeline failed"
        return 1
    fi
}

# Function to stream encode from local input to local output
stream_local_to_local() {
    local input_file="$1"
    local output_file="$2"

    echo "Streaming: $input_file -> $output_file"
    echo "Starting real-time local-to-local streaming compression..."
    mkdir -p "$(dirname "$output_file")"

    # Stream with progress logging
    ffmpeg -y -hide_banner -loglevel info -progress pipe:2 \
    -i "$input_file" -vcodec libx264 -crf 28 -preset superfast "$output_file"
}

# Function to handle original files after successful encoding
handle_originals() {
    local original_file="$1"

    # Keep original files in their original location - no moving
    echo "✓ Original file preserved: $original_file"

    # Optional: Add a comment or log entry about preservation
    if [[ "$INPUT_SOURCE" == s3://* ]]; then
        echo "  S3 original maintained at: $original_file"
    else
        echo "  Local original maintained at: $original_file"
    fi
}

# Main processing function
process_files() {
    local file_list
    file_list=$(get_file_list)
    
    if [ -z "$file_list" ]; then
        echo "No .y4m files found in input source"
        exit 1
    fi
    
    local file_count=0
    local success_count=0
    
    while IFS= read -r input_file; do
        [ -z "$input_file" ] && continue
        
        file_count=$((file_count + 1))
        base_name=$(basename "$input_file" .y4m)
        
        # Determine output path
        if [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
            # Remove trailing slash from output destination
            local output_dest="${OUTPUT_DESTINATION%/}"
            output_file="${output_dest}/${base_name}_zmt.mp4"
        else
            output_file="${OUTPUT_DESTINATION}/${base_name}_zmt.mp4"
        fi
        
        echo "Processing file $file_count: $(basename "$input_file")"
        
        # Choose streaming method based on input/output types
        if [[ "$INPUT_SOURCE" == s3://* ]] && [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
            stream_s3_to_s3 "$input_file" "$output_file"
        elif [[ "$INPUT_SOURCE" == s3://* ]]; then
            stream_s3_to_local "$input_file" "$output_file"
        elif [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
            stream_local_to_s3 "$input_file" "$output_file"
        else
            stream_local_to_local "$input_file" "$output_file"
        fi
        
        # Check if encoding was successful
        if [ $? -eq 0 ]; then
            echo "✓ Successfully streamed: ${base_name}_zmt.mp4"
            handle_originals "$input_file"
            success_count=$((success_count + 1))
        else
            echo "✗ Error streaming: $(basename "$input_file")"
        fi
        
    done <<< "$file_list"
    
    echo "Finished! Processed $success_count/$file_count files successfully."
}

# Validate dependencies
check_dependencies() {
    if ! command -v ffmpeg &> /dev/null; then
        echo "Error: ffmpeg is not installed"
        exit 1
    fi
    
    if [[ "$INPUT_SOURCE" == s3://* ]] || [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
        if ! command -v aws &> /dev/null; then
            echo "Error: AWS CLI is not installed"
            exit 1
        fi
    fi
}

# Main execution
check_dependencies
process_files
