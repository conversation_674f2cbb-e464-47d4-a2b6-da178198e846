#!/bin/bash

# Check if both input and output arguments are provided
if [ $# -ne 2 ]; then
    echo "Usage: $0 <input_source> <output_destination>"
    echo "Examples:"
    echo "  $0 /local/input/folder /local/output/folder"
    echo "  $0 s3://bucket/input/ /local/output/"
    echo "  $0 /local/input/ s3://bucket/output/"
    echo "  $0 s3://input-bucket/folder/ s3://output-bucket/folder/"
    exit 1
fi

INPUT_SOURCE="$1"
OUTPUT_DESTINATION="$2"

echo "Starting real-time streaming video encoding..."
echo "Input source: $INPUT_SOURCE"
echo "Output destination: $OUTPUT_DESTINATION"

# Function to get list of files from S3 or local
get_file_list() {
    if [[ "$INPUT_SOURCE" == s3://* ]]; then
        aws s3 ls "$INPUT_SOURCE" --recursive | grep "\.y4m$" | awk '{print $4}' | sed "s|^|s3://${INPUT_SOURCE#s3://}|" | sed 's|//|/|g' | sed 's|s3:/|s3://|'
    else
        find "$INPUT_SOURCE" -name "*.y4m" -type f
    fi
}

# Function to stream encode from S3 input to S3 output
stream_s3_to_s3() {
    local input_file="$1"
    local output_file="$2"
    
    echo "Streaming: $input_file -> $output_file"
    aws s3 cp "$input_file" - | ffmpeg -y -hide_banner -loglevel error -i pipe:0 -vcodec libx264 -crf 28 -preset superfast -f mp4 pipe:1 | aws s3 cp - "$output_file"
}

# Function to stream encode from S3 input to local output
stream_s3_to_local() {
    local input_file="$1"
    local output_file="$2"
    
    echo "Streaming: $input_file -> $output_file"
    mkdir -p "$(dirname "$output_file")"
    aws s3 cp "$input_file" - | ffmpeg -y -hide_banner -loglevel error -i pipe:0 -vcodec libx264 -crf 28 -preset superfast "$output_file"
}

# Function to stream encode from local input to S3 output
stream_local_to_s3() {
    local input_file="$1"
    local output_file="$2"
    
    echo "Streaming: $input_file -> $output_file"
    ffmpeg -y -hide_banner -loglevel error -i "$input_file" -vcodec libx264 -crf 28 -preset superfast -f mp4 pipe:1 | aws s3 cp - "$output_file"
}

# Function to stream encode from local input to local output
stream_local_to_local() {
    local input_file="$1"
    local output_file="$2"
    
    echo "Streaming: $input_file -> $output_file"
    mkdir -p "$(dirname "$output_file")"
    ffmpeg -y -hide_banner -loglevel error -i "$input_file" -vcodec libx264 -crf 28 -preset superfast "$output_file"
}

# Function to handle original files after successful encoding
handle_originals() {
    local original_file="$1"
    
    if [[ "$INPUT_SOURCE" == s3://* ]]; then
        # For S3 input, move to processed folder or delete
        processed_path="${original_file%/*}/processed/$(basename "$original_file")"
        echo "Moving original to: $processed_path"
        aws s3 mv "$original_file" "$processed_path"
    else
        # For local input, move to original folder
        mkdir -p original
        mv "$original_file" "original/"
        echo "Moved original to: original/$(basename "$original_file")"
    fi
}

# Main processing function
process_files() {
    local file_list
    file_list=$(get_file_list)
    
    if [ -z "$file_list" ]; then
        echo "No .y4m files found in input source"
        exit 1
    fi
    
    local file_count=0
    local success_count=0
    
    while IFS= read -r input_file; do
        [ -z "$input_file" ] && continue
        
        file_count=$((file_count + 1))
        base_name=$(basename "$input_file" .y4m)
        
        # Determine output path
        if [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
            output_file="${OUTPUT_DESTINATION}${base_name}_zmt.mp4"
            # Ensure proper S3 path formatting
            output_file=$(echo "$output_file" | sed 's|//*|/|g' | sed 's|s3:/|s3://|')
        else
            output_file="${OUTPUT_DESTINATION}/${base_name}_zmt.mp4"
        fi
        
        echo "Processing file $file_count: $(basename "$input_file")"
        
        # Choose streaming method based on input/output types
        if [[ "$INPUT_SOURCE" == s3://* ]] && [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
            stream_s3_to_s3 "$input_file" "$output_file"
        elif [[ "$INPUT_SOURCE" == s3://* ]]; then
            stream_s3_to_local "$input_file" "$output_file"
        elif [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
            stream_local_to_s3 "$input_file" "$output_file"
        else
            stream_local_to_local "$input_file" "$output_file"
        fi
        
        # Check if encoding was successful
        if [ $? -eq 0 ]; then
            echo "✓ Successfully streamed: ${base_name}_zmt.mp4"
            handle_originals "$input_file"
            success_count=$((success_count + 1))
        else
            echo "✗ Error streaming: $(basename "$input_file")"
        fi
        
    done <<< "$file_list"
    
    echo "Finished! Processed $success_count/$file_count files successfully."
}

# Validate dependencies
check_dependencies() {
    if ! command -v ffmpeg &> /dev/null; then
        echo "Error: ffmpeg is not installed"
        exit 1
    fi
    
    if [[ "$INPUT_SOURCE" == s3://* ]] || [[ "$OUTPUT_DESTINATION" == s3://* ]]; then
        if ! command -v aws &> /dev/null; then
            echo "Error: AWS CLI is not installed"
            exit 1
        fi
    fi
}

# Main execution
check_dependencies
process_files