# AWS CloudTrail Data Events Configuration

## 1. Enable CloudTrail Data Events for S3

### Step 1: Create CloudTrail for S3 Object-Level Tracking
```bash
# Create CloudTrail trail
aws cloudtrail create-trail \
    --name zmt-s3-data-events \
    --s3-bucket-name zmt-cloudtrail-logs \
    --include-global-service-events \
    --is-multi-region-trail \
    --enable-log-file-validation

# Create S3 bucket for CloudTrail logs
aws s3 mb s3://zmt-cloudtrail-logs --region us-west-1

# Set bucket policy for CloudTrail
aws s3api put-bucket-policy --bucket zmt-cloudtrail-logs --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AWSCloudTrailAclCheck",
      "Effect": "Allow",
      "Principal": {
        "Service": "cloudtrail.amazonaws.com"
      },
      "Action": "s3:GetBucketAcl",
      "Resource": "arn:aws:s3:::zmt-cloudtrail-logs"
    },
    {
      "Sid": "AWSCloudTrailWrite",
      "Effect": "Allow",
      "Principal": {
        "Service": "cloudtrail.amazonaws.com"
      },
      "Action": "s3:PutObject",
      "Resource": "arn:aws:s3:::zmt-cloudtrail-logs/*",
      "Condition": {
        "StringEquals": {
          "s3:x-amz-acl": "bucket-owner-full-control"
        }
      }
    }
  ]
}'
```

### Step 2: Configure Data Events for S3 Buckets
```bash
# Enable data events for source video bucket
aws cloudtrail put-event-selectors \
    --trail-name zmt-s3-data-events \
    --event-selectors '[
      {
        "ReadWriteType": "All",
        "IncludeManagementEvents": true,
        "DataResources": [
          {
            "Type": "AWS::S3::Object",
            "Values": [
              "arn:aws:s3:::zmt-source-video/*",
              "arn:aws:s3:::zmt-compressed-video/*"
            ]
          }
        ]
      }
    ]'

# Start logging
aws cloudtrail start-logging --name zmt-s3-data-events
```

### Step 3: Verify CloudTrail Configuration
```bash
# Check trail status
aws cloudtrail get-trail-status --name zmt-s3-data-events

# Verify event selectors
aws cloudtrail get-event-selectors --trail-name zmt-s3-data-events
```

## 2. CloudTrail Log Analysis

### CloudTrail Event Structure for S3
```json
{
  "eventVersion": "1.05",
  "userIdentity": {
    "type": "IAMUser",
    "principalId": "AIDACKCEVSQ6C2EXAMPLE",
    "arn": "arn:aws:iam::************:user/username",
    "accountId": "************",
    "userName": "username"
  },
  "eventTime": "2025-06-09T12:13:39Z",
  "eventSource": "s3.amazonaws.com",
  "eventName": "PutObject",
  "awsRegion": "us-west-1",
  "sourceIPAddress": "************",
  "userAgent": "aws-sdk-python/1.26.137",
  "requestParameters": {
    "bucketName": "zmt-compressed-video",
    "key": "hls/enhanced_logging_big_test/stream_0138.ts",
    "x-amz-server-side-encryption": "AES256"
  },
  "responseElements": {
    "x-amz-request-id": "79A59DF900B949E5",
    "x-amz-id-2": "example-id-2"
  },
  "s3": {
    "s3SchemaVersion": "1.0",
    "configurationId": "example-id",
    "bucket": {
      "name": "zmt-compressed-video",
      "ownerIdentity": {
        "principalId": "A3NL1KOZZKExample"
      },
      "arn": "arn:aws:s3:::zmt-compressed-video"
    },
    "object": {
      "key": "hls/enhanced_logging_big_test/stream_0138.ts",
      "size": 194016,
      "eTag": "example-etag",
      "sequencer": "example-sequencer"
    }
  }
}
```

### Python Script for CloudTrail Analysis
```python
#!/usr/bin/env python3
import boto3
import json
import gzip
from datetime import datetime, timedelta
from collections import defaultdict

def analyze_cloudtrail_s3_events(bucket_name, prefix, start_time, end_time):
    """
    Analyze CloudTrail logs for S3 data events
    """
    s3 = boto3.client('s3')
    
    stats = {
        'total_events': 0,
        'put_events': 0,
        'get_events': 0,
        'delete_events': 0,
        'total_bytes_uploaded': 0,
        'total_bytes_downloaded': 0,
        'users': defaultdict(int),
        'buckets': defaultdict(int),
        'hourly_activity': defaultdict(lambda: {'puts': 0, 'gets': 0, 'bytes': 0})
    }
    
    # List CloudTrail log files
    response = s3.list_objects_v2(
        Bucket=bucket_name,
        Prefix=prefix,
        StartAfter=f"{prefix}{start_time.strftime('%Y/%m/%d')}",
        EndBefore=f"{prefix}{end_time.strftime('%Y/%m/%d')}"
    )
    
    for obj in response.get('Contents', []):
        if not obj['Key'].endswith('.json.gz'):
            continue
            
        # Download and decompress log file
        log_obj = s3.get_object(Bucket=bucket_name, Key=obj['Key'])
        
        with gzip.GzipFile(fileobj=log_obj['Body']) as gzf:
            log_data = json.loads(gzf.read().decode('utf-8'))
            
        # Process each record
        for record in log_data.get('Records', []):
            if record.get('eventSource') != 's3.amazonaws.com':
                continue
                
            event_name = record.get('eventName', '')
            event_time = datetime.fromisoformat(record.get('eventTime', '').replace('Z', '+00:00'))
            
            # Filter by time range
            if not (start_time <= event_time <= end_time):
                continue
                
            stats['total_events'] += 1
            
            # Extract user information
            user_identity = record.get('userIdentity', {})
            user_name = user_identity.get('userName', user_identity.get('type', 'Unknown'))
            stats['users'][user_name] += 1
            
            # Extract S3 information
            s3_info = record.get('requestParameters', {})
            bucket_name_event = s3_info.get('bucketName', 'Unknown')
            stats['buckets'][bucket_name_event] += 1
            
            # Track by hour
            hour_key = event_time.strftime('%Y-%m-%d %H:00')
            
            # Analyze event types
            if event_name == 'PutObject':
                stats['put_events'] += 1
                stats['hourly_activity'][hour_key]['puts'] += 1
                
                # Extract object size from response elements or s3 section
                object_size = 0
                if 's3' in record and 'object' in record['s3']:
                    object_size = record['s3']['object'].get('size', 0)
                
                stats['total_bytes_uploaded'] += object_size
                stats['hourly_activity'][hour_key]['bytes'] += object_size
                
            elif event_name == 'GetObject':
                stats['get_events'] += 1
                stats['hourly_activity'][hour_key]['gets'] += 1
                
                # For GET events, we'd need to correlate with access logs for bytes transferred
                
            elif event_name in ['DeleteObject', 'DeleteObjects']:
                stats['delete_events'] += 1
    
    return stats

def generate_cloudtrail_report(stats):
    """
    Generate CloudTrail analysis report
    """
    print("="*80)
    print("CLOUDTRAIL S3 DATA EVENTS REPORT")
    print("="*80)
    
    print(f"Total S3 Events: {stats['total_events']:,}")
    print(f"PUT Events: {stats['put_events']:,}")
    print(f"GET Events: {stats['get_events']:,}")
    print(f"DELETE Events: {stats['delete_events']:,}")
    print(f"Total Bytes Uploaded: {stats['total_bytes_uploaded']:,} ({stats['total_bytes_uploaded']/1024/1024:.2f} MB)")
    
    print("\nTop Users:")
    for user, count in sorted(stats['users'].items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {user}: {count:,} events")
    
    print("\nBucket Activity:")
    for bucket, count in sorted(stats['buckets'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {bucket}: {count:,} events")
    
    print("\nHourly Activity (Top 10):")
    for hour, activity in sorted(stats['hourly_activity'].items(), 
                                key=lambda x: x[1]['puts'] + x[1]['gets'], reverse=True)[:10]:
        print(f"  {hour}: {activity['puts']} PUTs, {activity['gets']} GETs, {activity['bytes']/1024/1024:.2f} MB")

# Example usage
if __name__ == "__main__":
    start_time = datetime.now() - timedelta(days=1)
    end_time = datetime.now()
    
    stats = analyze_cloudtrail_s3_events(
        bucket_name='zmt-cloudtrail-logs',
        prefix='AWSLogs/************/CloudTrail/us-west-1/',
        start_time=start_time,
        end_time=end_time
    )
    
    generate_cloudtrail_report(stats)
```

## 3. AWS Athena Integration for Log Querying

### Create Athena Table for CloudTrail Logs
```sql
CREATE EXTERNAL TABLE cloudtrail_logs (
    eventversion STRING,
    useridentity STRUCT<
        type: STRING,
        principalid: STRING,
        arn: STRING,
        accountid: STRING,
        invokedby: STRING,
        accesskeyid: STRING,
        userName: STRING,
        sessioncontext: STRUCT<
            attributes: STRUCT<
                mfaauthenticated: STRING,
                creationdate: STRING>,
            sessionissuer: STRUCT<
                type: STRING,
                principalId: STRING,
                arn: STRING,
                accountId: STRING,
                userName: STRING>>>,
    eventtime STRING,
    eventsource STRING,
    eventname STRING,
    awsregion STRING,
    sourceipaddress STRING,
    useragent STRING,
    errorcode STRING,
    errormessage STRING,
    requestparameters STRING,
    responseelements STRING,
    additionaleventdata STRING,
    requestid STRING,
    eventid STRING,
    resources ARRAY<STRUCT<
        ARN: STRING,
        accountId: STRING,
        type: STRING>>,
    eventtype STRING,
    apiversion STRING,
    readonly STRING,
    recipientaccountid STRING,
    serviceeventdetails STRING,
    sharedeventid STRING,
    vpcendpointid STRING
)
PARTITIONED BY (
   account string,
   region string,
   year string,
   month string,
   day string
)
STORED AS INPUTFORMAT 'com.amazon.emr.cloudtrail.CloudTrailInputFormat'
OUTPUTFORMAT 'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat'
LOCATION 's3://zmt-cloudtrail-logs/AWSLogs/'
TBLPROPERTIES ('classification'='cloudtrail');
```

### Sample Athena Queries
```sql
-- Get S3 PUT operations for the last 24 hours
SELECT 
    eventtime,
    useridentity.username,
    json_extract_scalar(requestparameters, '$.bucketName') as bucket,
    json_extract_scalar(requestparameters, '$.key') as object_key,
    sourceipaddress
FROM cloudtrail_logs
WHERE eventname = 'PutObject'
    AND eventsource = 's3.amazonaws.com'
    AND eventtime >= date_format(date_add('hour', -24, now()), '%Y-%m-%dT%H:%i:%sZ')
ORDER BY eventtime DESC;

-- Calculate bandwidth usage by hour
SELECT 
    date_trunc('hour', from_iso8601_timestamp(eventtime)) as hour,
    eventname,
    count(*) as request_count,
    json_extract_scalar(requestparameters, '$.bucketName') as bucket
FROM cloudtrail_logs
WHERE eventsource = 's3.amazonaws.com'
    AND eventname IN ('PutObject', 'GetObject')
    AND year = '2025' AND month = '06' AND day = '09'
GROUP BY 1, 2, 4
ORDER BY 1, 2;
```
