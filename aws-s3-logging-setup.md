# AWS S3 Server Access Logging Setup

## 1. Enable S3 Server Access Logging

### Step 1: Create a Dedicated Logging Bucket
```bash
# Create a separate bucket for access logs
aws s3 mb s3://zmt-s3-access-logs --region us-west-1

# Set up bucket policy for logging access
aws s3api put-bucket-policy --bucket zmt-s3-access-logs --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "S3ServerAccessLogsPolicy",
      "Effect": "Allow",
      "Principal": {
        "Service": "logging.s3.amazonaws.com"
      },
      "Action": "s3:PutObject",
      "Resource": "arn:aws:s3:::zmt-s3-access-logs/*",
      "Condition": {
        "ArnLike": {
          "aws:SourceArn": "arn:aws:s3:::zmt-compressed-video"
        }
      }
    },
    {
      "Sid": "S3ServerAccessLogsDelivery",
      "Effect": "Allow",
      "Principal": {
        "Service": "logging.s3.amazonaws.com"
      },
      "Action": "s3:GetBucketAcl",
      "Resource": "arn:aws:s3:::zmt-s3-access-logs"
    }
  ]
}'
```

### Step 2: Enable Logging on Target Buckets
```bash
# Enable access logging for compressed video bucket
aws s3api put-bucket-logging --bucket zmt-compressed-video --bucket-logging-status '{
  "LoggingEnabled": {
    "TargetBucket": "zmt-s3-access-logs",
    "TargetPrefix": "compressed-video-logs/"
  }
}'

# Enable access logging for source video bucket
aws s3api put-bucket-logging --bucket zmt-source-video --bucket-logging-status '{
  "LoggingEnabled": {
    "TargetBucket": "zmt-s3-access-logs",
    "TargetPrefix": "source-video-logs/"
  }
}'
```

### Step 3: Verify Logging Configuration
```bash
# Check logging status
aws s3api get-bucket-logging --bucket zmt-compressed-video
aws s3api get-bucket-logging --bucket zmt-source-video
```

## 2. Understanding S3 Access Log Format

### Log File Structure
Access logs are delivered as text files with the following format:
```
bucket-owner canonical-user-id timestamp remote-ip requester operation bucket key request-uri http-status error-code bytes-sent object-size total-time turn-around-time referrer user-agent version-id host-id signature-version cipher-suite authentication-type host-header tls-version
```

### Key Fields for Bandwidth Tracking
- **operation**: Type of request (REST.PUT.OBJECT, REST.GET.OBJECT, etc.)
- **bytes-sent**: Number of bytes sent in response
- **object-size**: Size of the object in bytes
- **timestamp**: When the request was processed
- **requester**: AWS account or user making the request
- **key**: Object key (filename)

### Example Log Entry
```
zmt-compressed-video 79a59df900b949e55d96a1e698fbacedfd6e09d98eacf8f8d5218e7cd47ef2be [09/Jun/2025:12:13:39 +0000] *********** 79a59df900b949e55d96a1e698fbacedfd6e09d98eacf8f8d5218e7cd47ef2be REST.PUT.OBJECT hls/enhanced_logging_big_test/stream_0138.ts "PUT /hls/enhanced_logging_big_test/stream_0138.ts HTTP/1.1" 200 - - 194016 28 12 "-" "aws-sdk-python/1.26.137 Python/3.9.16" - TjS8J2U1Hy4fF2P4 SigV4 ECDHE-RSA-AES128-GCM-SHA256 AuthHeader s3.us-west-1.amazonaws.com TLSv1.2
```

## 3. Automated Log Analysis Script

### Python Script for Bandwidth Analysis
```python
#!/usr/bin/env python3
import boto3
import re
from datetime import datetime, timedelta
from collections import defaultdict

def analyze_s3_access_logs(bucket_name, prefix, start_date, end_date):
    """
    Analyze S3 access logs for bandwidth usage
    """
    s3 = boto3.client('s3')
    
    # Statistics tracking
    stats = {
        'total_requests': 0,
        'total_bytes_sent': 0,
        'total_bytes_received': 0,
        'operations': defaultdict(int),
        'hourly_bandwidth': defaultdict(int),
        'daily_bandwidth': defaultdict(int)
    }
    
    # List log files in date range
    response = s3.list_objects_v2(
        Bucket=bucket_name,
        Prefix=prefix,
        StartAfter=f"{prefix}{start_date.strftime('%Y-%m-%d')}",
        EndBefore=f"{prefix}{end_date.strftime('%Y-%m-%d')}"
    )
    
    for obj in response.get('Contents', []):
        # Download and parse each log file
        log_content = s3.get_object(Bucket=bucket_name, Key=obj['Key'])['Body'].read().decode('utf-8')
        
        for line in log_content.strip().split('\n'):
            if not line:
                continue
                
            # Parse log line
            fields = parse_log_line(line)
            if not fields:
                continue
                
            # Extract metrics
            timestamp = fields.get('timestamp')
            operation = fields.get('operation')
            bytes_sent = int(fields.get('bytes_sent', 0) or 0)
            object_size = int(fields.get('object_size', 0) or 0)
            
            # Update statistics
            stats['total_requests'] += 1
            stats['total_bytes_sent'] += bytes_sent
            stats['operations'][operation] += 1
            
            # Track bandwidth by time
            if timestamp:
                hour_key = timestamp.strftime('%Y-%m-%d %H:00')
                day_key = timestamp.strftime('%Y-%m-%d')
                stats['hourly_bandwidth'][hour_key] += bytes_sent
                stats['daily_bandwidth'][day_key] += bytes_sent
                
            # Track uploads (PUT operations)
            if operation.startswith('REST.PUT'):
                stats['total_bytes_received'] += object_size
    
    return stats

def parse_log_line(line):
    """
    Parse S3 access log line into fields
    """
    # S3 access log regex pattern
    pattern = r'(\S+) (\S+) \[([^\]]+)\] (\S+) (\S+) (\S+) (\S+) "([^"]*)" (\S+) (\S+) (\S+) (\S+) (\S+) (\S+) "([^"]*)" "([^"]*)" (\S+) (\S+) (\S+) (\S+) (\S+) (\S+) (\S+)'
    
    match = re.match(pattern, line)
    if not match:
        return None
        
    groups = match.groups()
    
    return {
        'bucket_owner': groups[0],
        'bucket': groups[1],
        'timestamp': datetime.strptime(groups[2], '%d/%b/%Y:%H:%M:%S %z'),
        'remote_ip': groups[3],
        'requester': groups[4],
        'request_id': groups[5],
        'operation': groups[6],
        'key': groups[7],
        'request_uri': groups[8],
        'http_status': groups[9],
        'error_code': groups[10],
        'bytes_sent': groups[11],
        'object_size': groups[12],
        'total_time': groups[13],
        'turn_around_time': groups[14],
        'referrer': groups[15],
        'user_agent': groups[16],
        'version_id': groups[17]
    }

def generate_bandwidth_report(stats):
    """
    Generate comprehensive bandwidth report
    """
    print("="*80)
    print("S3 BANDWIDTH USAGE REPORT")
    print("="*80)
    
    print(f"Total Requests: {stats['total_requests']:,}")
    print(f"Total Data Sent: {stats['total_bytes_sent']:,} bytes ({stats['total_bytes_sent']/1024/1024:.2f} MB)")
    print(f"Total Data Received: {stats['total_bytes_received']:,} bytes ({stats['total_bytes_received']/1024/1024:.2f} MB)")
    
    print("\nOperations Breakdown:")
    for operation, count in sorted(stats['operations'].items()):
        print(f"  {operation}: {count:,} requests")
    
    print("\nDaily Bandwidth Usage:")
    for day, bytes_used in sorted(stats['daily_bandwidth'].items()):
        print(f"  {day}: {bytes_used:,} bytes ({bytes_used/1024/1024:.2f} MB)")

if __name__ == "__main__":
    # Example usage
    start_date = datetime.now() - timedelta(days=7)
    end_date = datetime.now()
    
    stats = analyze_s3_access_logs(
        bucket_name='zmt-s3-access-logs',
        prefix='compressed-video-logs/',
        start_date=start_date,
        end_date=end_date
    )
    
    generate_bandwidth_report(stats)
```

## 4. Cost Estimation from Logs

### Calculate S3 Costs
```python
def calculate_s3_costs(stats, region='us-west-1'):
    """
    Calculate estimated S3 costs based on usage statistics
    """
    # S3 pricing (as of 2025 - verify current rates)
    pricing = {
        'storage_gb_month': 0.023,  # Standard storage
        'requests_put_1000': 0.005,  # PUT requests per 1000
        'requests_get_1000': 0.0004,  # GET requests per 1000
        'data_transfer_gb': 0.09,  # Data transfer out
    }
    
    # Calculate costs
    put_requests = sum(count for op, count in stats['operations'].items() if 'PUT' in op)
    get_requests = sum(count for op, count in stats['operations'].items() if 'GET' in op)
    
    costs = {
        'put_requests': (put_requests / 1000) * pricing['requests_put_1000'],
        'get_requests': (get_requests / 1000) * pricing['requests_get_1000'],
        'data_transfer': (stats['total_bytes_sent'] / 1024**3) * pricing['data_transfer_gb']
    }
    
    costs['total'] = sum(costs.values())
    
    return costs
```
