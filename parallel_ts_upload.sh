#!/bin/bash

# Parallel .ts File Upload Script
# Uploads multiple .ts files from a local directory to S3 using parallel threads
# Usage: ./parallel_ts_upload.sh <local_directory> <s3_bucket> <s3_prefix> [threads] [region]

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')]${NC} ✅ $1"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')]${NC} ⚠️  $1"
}

print_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')]${NC} ❌ $1"
}

# Function to upload a single file
upload_file() {
    local file_path="$1"
    local s3_bucket="$2"
    local s3_prefix="$3"
    local region="$4"
    local thread_id="$5"
    
    local filename=$(basename "$file_path")
    local file_size=$(stat -c%s "$file_path" 2>/dev/null || stat -f%z "$file_path" 2>/dev/null || echo "0")
    local s3_key="${s3_prefix}${filename}"
    
    # Format file size for display
    local size_display
    if [ "$file_size" -ge 1048576 ]; then
        size_display=$(echo "scale=2; $file_size / 1048576" | bc -l)" MB"
    elif [ "$file_size" -ge 1024 ]; then
        size_display=$(echo "scale=2; $file_size / 1024" | bc -l)" KB"
    else
        size_display="${file_size} bytes"
    fi
    
    print_status "[Thread $thread_id] Uploading: $filename ($size_display)"
    
    # Record start time
    local start_time=$(date +%s.%N)
    
    # Upload file to S3
    local upload_cmd="aws s3 cp \"$file_path\" \"s3://$s3_bucket/$s3_key\""
    if [ -n "$region" ]; then
        upload_cmd="$upload_cmd --region $region"
    fi
    
    if eval $upload_cmd 2>/dev/null; then
        # Calculate upload time and speed
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc -l)
        local speed_mbps=$(echo "scale=2; ($file_size / 1048576) / $duration" | bc -l)
        
        print_success "[Thread $thread_id] ⏱️ Uploaded: $filename | Size: $size_display | Speed: ${speed_mbps} MB/s | Duration: ${duration}s"
        echo "$filename:$file_size:$duration:$speed_mbps" >> "/tmp/upload_results_$$"
    else
        print_error "[Thread $thread_id] Failed to upload: $filename"
        echo "FAILED:$filename" >> "/tmp/upload_failures_$$"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 <local_directory> <s3_bucket> <s3_prefix> [threads] [region]"
    echo ""
    echo "Parameters:"
    echo "  local_directory  - Local directory containing .ts files"
    echo "  s3_bucket       - S3 bucket name (without s3:// prefix)"
    echo "  s3_prefix       - S3 key prefix (path within bucket, should end with /)"
    echo "  threads         - Number of parallel threads (default: 6, range: 1-8)"
    echo "  region          - AWS region (default: from AWS_REGION env var)"
    echo ""
    echo "Examples:"
    echo "  $0 ./hls_output my-bucket output/stream/ 4 us-east-1"
    echo "  $0 /tmp/segments video-bucket streams/live/ 6"
    echo "  $0 ./segments bucket path/to/stream/"
    echo ""
    echo "Environment Variables:"
    echo "  AWS_REGION      - Default AWS region if not specified"
    echo "  AWS_PROFILE     - AWS profile to use"
}

# Check if help is requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
fi

# Validate arguments
if [ $# -lt 3 ]; then
    print_error "Missing required arguments"
    show_usage
    exit 1
fi

LOCAL_DIR="$1"
S3_BUCKET="$2"
S3_PREFIX="$3"
THREADS="${4:-6}"  # Default to 6 threads
REGION="${5:-$AWS_REGION}"  # Default to AWS_REGION env var if not specified

# Validate inputs
if [ ! -d "$LOCAL_DIR" ]; then
    print_error "Local directory does not exist: $LOCAL_DIR"
    exit 1
fi

if [ "$THREADS" -lt 1 ] || [ "$THREADS" -gt 8 ]; then
    print_error "Thread count must be between 1 and 8"
    exit 1
fi

# Ensure S3 prefix ends with /
if [ -n "$S3_PREFIX" ] && [ "${S3_PREFIX: -1}" != "/" ]; then
    S3_PREFIX="${S3_PREFIX}/"
fi

# Check for required tools
for tool in aws bc; do
    if ! command -v $tool &> /dev/null; then
        print_error "Required tool not found: $tool"
        exit 1
    fi
done

# Check AWS credentials
if ! aws sts get-caller-identity &>/dev/null; then
    print_error "AWS credentials not configured or invalid"
    exit 1
fi

# Find all .ts files
print_status "Scanning for .ts files in: $LOCAL_DIR"
TS_FILES=($(find "$LOCAL_DIR" -name "*.ts" -type f | sort))

if [ ${#TS_FILES[@]} -eq 0 ]; then
    print_warning "No .ts files found in directory: $LOCAL_DIR"
    exit 0
fi

print_status "Found ${#TS_FILES[@]} .ts files to upload"
print_status "Using $THREADS parallel threads"
print_status "Target: s3://$S3_BUCKET/$S3_PREFIX"
if [ -n "$REGION" ]; then
    print_status "Region: $REGION"
fi

# Initialize result files
echo "" > "/tmp/upload_results_$$"
echo "" > "/tmp/upload_failures_$$"

# Start upload process
print_status "🚀 Starting parallel upload..."
start_time=$(date +%s)

# Export function for parallel execution
export -f upload_file print_status print_success print_error

# Use GNU parallel if available, otherwise use xargs
if command -v parallel &> /dev/null; then
    printf '%s\n' "${TS_FILES[@]}" | parallel -j "$THREADS" upload_file {} "$S3_BUCKET" "$S3_PREFIX" "$REGION" {%}
else
    printf '%s\n' "${TS_FILES[@]}" | xargs -n 1 -P "$THREADS" -I {} bash -c 'upload_file "$@"' _ {} "$S3_BUCKET" "$S3_PREFIX" "$REGION" "$$"
fi

# Calculate total time
end_time=$(date +%s)
total_duration=$((end_time - start_time))

# Process results
successful_uploads=$(grep -v "^$" "/tmp/upload_results_$$" | wc -l)
failed_uploads=$(grep -v "^$" "/tmp/upload_failures_$$" | wc -l)

# Calculate statistics
total_size=0
total_upload_time=0
if [ -s "/tmp/upload_results_$$" ]; then
    while IFS=':' read -r filename size duration speed; do
        if [ -n "$size" ] && [ "$size" != "FAILED" ]; then
            total_size=$((total_size + size))
            total_upload_time=$(echo "$total_upload_time + $duration" | bc -l)
        fi
    done < "/tmp/upload_results_$$"
fi

# Format total size
if [ "$total_size" -ge 1073741824 ]; then
    size_display=$(echo "scale=2; $total_size / 1073741824" | bc -l)" GB"
elif [ "$total_size" -ge 1048576 ]; then
    size_display=$(echo "scale=2; $total_size / 1048576" | bc -l)" MB"
else
    size_display=$(echo "scale=2; $total_size / 1024" | bc -l)" KB"
fi

# Calculate average speed
if [ "$total_duration" -gt 0 ]; then
    avg_speed=$(echo "scale=2; ($total_size / 1048576) / $total_duration" | bc -l)
else
    avg_speed="0.00"
fi

# Print summary
echo ""
echo "================================================================================"
print_status "📊 UPLOAD SUMMARY"
echo "================================================================================"
print_success "✅ Successful uploads: $successful_uploads"
if [ "$failed_uploads" -gt 0 ]; then
    print_error "❌ Failed uploads: $failed_uploads"
fi
print_status "📦 Total data uploaded: $size_display"
print_status "⏱️  Total time: ${total_duration}s"
print_status "🚀 Average speed: ${avg_speed} MB/s"
print_status "🧵 Threads used: $THREADS"
print_status "📍 Destination: s3://$S3_BUCKET/$S3_PREFIX"

# Show failed files if any
if [ "$failed_uploads" -gt 0 ]; then
    echo ""
    print_warning "Failed uploads:"
    grep "FAILED:" "/tmp/upload_failures_$$" | sed 's/FAILED:/  - /'
fi

# Cleanup
rm -f "/tmp/upload_results_$$" "/tmp/upload_failures_$$"

echo "================================================================================"

if [ "$failed_uploads" -eq 0 ]; then
    print_success "🎉 All uploads completed successfully!"
    exit 0
else
    print_error "Some uploads failed. Check the summary above."
    exit 1
fi
