# 🚀 Parallel .ts File Upload Scripts

## 📋 **Overview**

Two powerful scripts for uploading multiple .ts files from a local directory to S3 using 4-8 parallel threads for maximum efficiency.

### **Scripts Included:**
- **`parallel_ts_upload.sh`** - Bash version (Linux/macOS)
- **`parallel_ts_upload.py`** - Python version (Cross-platform)

---

## 🎯 **Features**

### **Performance:**
- ✅ **4-8 parallel threads** - Configurable concurrency
- ✅ **Batch processing** - Upload multiple files simultaneously
- ✅ **Speed optimization** - Maximize bandwidth utilization
- ✅ **Progress tracking** - Real-time upload status

### **Monitoring:**
- ✅ **Detailed metrics** - File size, upload speed, duration
- ✅ **Color-coded output** - Easy visual status identification
- ✅ **Comprehensive summary** - Total data, time, average speed
- ✅ **Error reporting** - Failed uploads with reasons

### **Reliability:**
- ✅ **Error handling** - Graceful failure management
- ✅ **AWS credential validation** - Pre-flight checks
- ✅ **Input validation** - Directory and parameter verification
- ✅ **Resume capability** - Can re-run for failed uploads

---

## 🛠️ **Installation & Setup**

### **Prerequisites:**
```bash
# For Bash version:
- AWS CLI configured
- bc calculator (usually pre-installed)
- GNU parallel (optional, for better performance)

# For Python version:
- Python 3.6+
- boto3 library
```

### **Install Dependencies:**
```bash
# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Install Python dependencies
pip3 install boto3

# Install GNU parallel (optional, for bash version)
sudo apt-get install parallel  # Ubuntu/Debian
brew install parallel          # macOS
```

### **Configure AWS:**
```bash
aws configure
# Enter your AWS Access Key ID, Secret Access Key, Region, and Output format
```

---

## 🚀 **Usage**

### **Bash Version:**
```bash
./parallel_ts_upload.sh <local_directory> <s3_bucket> <s3_prefix> [threads] [region]
```

### **Python Version:**
```bash
python3 parallel_ts_upload.py <local_directory> <s3_bucket> <s3_prefix> --threads <num> --region <region>
```

### **Parameters:**
- **`local_directory`** - Local directory containing .ts files
- **`s3_bucket`** - S3 bucket name (without s3:// prefix)
- **`s3_prefix`** - S3 key prefix (path within bucket, should end with /)
- **`threads`** - Number of parallel threads (default: 6, range: 1-8)
- **`region`** - AWS region (default: from AWS_REGION env var)

---

## 📝 **Examples**

### **Basic Usage:**
```bash
# Bash version
./parallel_ts_upload.sh ./hls_output my-bucket output/stream/ 4 us-east-1

# Python version
python3 parallel_ts_upload.py ./hls_output my-bucket output/stream/ --threads 4 --region us-east-1
```

### **Advanced Examples:**
```bash
# Upload with 6 threads (default)
./parallel_ts_upload.sh /tmp/segments video-bucket streams/live/

# Upload with maximum threads
python3 parallel_ts_upload.py ./segments bucket path/to/stream/ --threads 8

# Upload using environment region
export AWS_REGION=us-west-2
./parallel_ts_upload.sh ./hls_segments my-streaming-bucket live/stream1/
```

### **Real-world HLS Upload:**
```bash
# Upload HLS segments after compression
./parallel_ts_upload.sh /tmp/hls_streaming_abc123/ zmt-compressed-video output/big_buck_bunny_360p24/ 6 us-west-1
```

---

## 📊 **Output Example**

```
[14:32:15] Scanning for .ts files in: ./hls_output
[14:32:15] Found 139 .ts files to upload
[14:32:15] Using 6 parallel threads
[14:32:15] Target: s3://my-bucket/output/stream/
[14:32:15] Region: us-east-1
[14:32:15] 🚀 Starting parallel upload...
[14:32:16] [Thread 1] Uploading: stream_0000.ts (451.46 KB)
[14:32:16] [Thread 2] Uploading: stream_0001.ts (121.17 KB)
[14:32:16] [Thread 3] Uploading: stream_0002.ts (497.36 KB)
[14:32:17] ✅ [Thread 1] ⏱️ Uploaded: stream_0000.ts | Size: 451.46 KB | Speed: 2.34 MB/s | Duration: 0.19s
[14:32:17] ✅ [Thread 2] ⏱️ Uploaded: stream_0001.ts | Size: 121.17 KB | Speed: 1.87 MB/s | Duration: 0.06s
...

================================================================================
[14:35:42] 📊 UPLOAD SUMMARY
================================================================================
✅ Successful uploads: 139
📦 Total data uploaded: 35.47 MB
⏱️  Total time: 207s
🚀 Average speed: 0.17 MB/s
🧵 Threads used: 6
📍 Destination: s3://my-bucket/output/stream/
================================================================================
✅ 🎉 All uploads completed successfully!
```

---

## ⚙️ **Configuration**

### **Thread Optimization:**
- **1-2 threads** - Slow connections or small files
- **4-6 threads** - Balanced performance (recommended)
- **6-8 threads** - High-bandwidth connections

### **Environment Variables:**
```bash
export AWS_REGION=us-east-1        # Default region
export AWS_PROFILE=production      # AWS profile to use
export AWS_ACCESS_KEY_ID=...       # AWS credentials
export AWS_SECRET_ACCESS_KEY=...   # AWS credentials
```

---

## 🔧 **Integration Examples**

### **With HLS Compression:**
```bash
#!/bin/bash
# Complete HLS workflow

# 1. Run compression
python3 hls_streaming_s3.py input.y4m s3://bucket/output/stream/ us-east-1 us-west-1

# 2. Upload any remaining local segments
./parallel_ts_upload.sh /tmp/hls_streaming_* bucket output/stream/ 6 us-west-1
```

### **Batch Processing Multiple Streams:**
```bash
#!/bin/bash
# Upload multiple HLS streams

for stream_dir in /tmp/hls_*; do
    stream_name=$(basename "$stream_dir")
    echo "Uploading stream: $stream_name"
    ./parallel_ts_upload.sh "$stream_dir" my-bucket "streams/$stream_name/" 4
done
```

---

## 🚨 **Error Handling**

### **Common Issues:**
- **AWS credentials not configured** - Run `aws configure`
- **Directory not found** - Check local directory path
- **S3 bucket access denied** - Verify bucket permissions
- **Network timeouts** - Reduce thread count

### **Retry Failed Uploads:**
```bash
# The scripts will show failed files in the summary
# Re-run the same command to retry failed uploads
./parallel_ts_upload.sh ./segments bucket path/ 4
```

---

## 📈 **Performance Tips**

### **Optimization:**
- **Use 4-6 threads** for most scenarios
- **Ensure good network bandwidth** before increasing threads
- **Monitor AWS costs** - S3 PUT requests are charged per request
- **Use appropriate instance types** if running on EC2

### **Monitoring:**
- **Watch upload speeds** - Should be consistent across threads
- **Check AWS CloudWatch** - Monitor S3 request metrics
- **Use S3 Transfer Acceleration** for global uploads

---

## 🎯 **Use Cases**

### **Perfect For:**
- ✅ **HLS segment uploads** - Batch upload after compression
- ✅ **Backup operations** - Fast backup of video segments
- ✅ **Migration tasks** - Moving .ts files to cloud storage
- ✅ **CI/CD pipelines** - Automated video processing workflows

### **Integration Points:**
- **Post-compression** - Upload segments after HLS encoding
- **Cleanup operations** - Upload then delete local files
- **Monitoring systems** - Track upload progress and failures
- **Backup strategies** - Regular segment archival

---

## 🏆 **Summary**

**Two powerful scripts for efficient .ts file uploads:**

- **🚀 Parallel processing** - 4-8 concurrent uploads
- **📊 Comprehensive monitoring** - Real-time progress and statistics
- **🛡️ Error handling** - Graceful failure management and reporting
- **🎯 Optimized performance** - Maximum bandwidth utilization
- **🔧 Easy integration** - Works with existing HLS workflows

**Choose the version that fits your environment:**
- **Bash version** - Lightweight, minimal dependencies
- **Python version** - Cross-platform, advanced features

Both scripts provide the same core functionality with excellent performance for batch .ts file uploads! 🎉
