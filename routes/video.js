const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const s3Service = require('../services/s3Service');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    // Accept Y4M files and common video formats
    const allowedTypes = ['.y4m', '.mp4', '.avi', '.mov', '.mkv'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Only video files are allowed'), false);
    }
  }
});

// Upload video file
router.post('/upload', upload.single('video'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const videoInfo = {
      filename: req.file.filename,
      originalName: req.file.originalname,
      path: req.file.path,
      size: req.file.size,
      mimetype: req.file.mimetype,
      url: `/videos/${req.file.filename}`
    };

    res.json({
      success: true,
      message: 'Video uploaded successfully',
      video: videoInfo
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
});

// Get video from S3
router.post('/s3', async (req, res) => {
  try {
    const { s3Uri } = req.body;
    
    if (!s3Uri) {
      return res.status(400).json({ error: 'S3 URI is required' });
    }

    // Parse S3 URI (s3://bucket/key)
    const s3Match = s3Uri.match(/^s3:\/\/([^\/]+)\/(.+)$/);
    if (!s3Match) {
      return res.status(400).json({ error: 'Invalid S3 URI format' });
    }

    const [, bucket, key] = s3Match;
    
    // Download file from S3
    const localPath = await s3Service.downloadFile(bucket, key);
    
    const videoInfo = {
      filename: path.basename(key),
      originalName: path.basename(key),
      path: localPath,
      s3Uri: s3Uri,
      url: `/videos/${path.basename(localPath)}`
    };

    res.json({
      success: true,
      message: 'Video downloaded from S3 successfully',
      video: videoInfo
    });
  } catch (error) {
    console.error('S3 download error:', error);
    res.status(500).json({ error: 'Failed to download from S3' });
  }
});

// List local videos
router.get('/list', (req, res) => {
  try {
    const uploadsDir = 'uploads';
    const files = fs.readdirSync(uploadsDir)
      .filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ['.y4m', '.mp4', '.avi', '.mov', '.mkv'].includes(ext);
      })
      .map(file => ({
        filename: file,
        path: path.join(uploadsDir, file),
        url: `/videos/${file}`,
        size: fs.statSync(path.join(uploadsDir, file)).size
      }));

    res.json({
      success: true,
      videos: files
    });
  } catch (error) {
    console.error('List videos error:', error);
    res.status(500).json({ error: 'Failed to list videos' });
  }
});

// Get video metadata
router.get('/metadata/:filename', (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join('uploads', filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'Video not found' });
    }

    const stats = fs.statSync(filePath);
    const metadata = {
      filename: filename,
      path: filePath,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      url: `/videos/${filename}`
    };

    res.json({
      success: true,
      metadata: metadata
    });
  } catch (error) {
    console.error('Metadata error:', error);
    res.status(500).json({ error: 'Failed to get metadata' });
  }
});

module.exports = router;
