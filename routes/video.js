const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const s3Service = require('../services/s3Service');

const router = express.Router();

// Function to create web-compatible preview for Y4M files
async function createWebPreview(originalPath, filename) {
  return new Promise((resolve, reject) => {
    const ext = path.extname(filename).toLowerCase();

    // Only create preview for Y4M files
    if (ext !== '.y4m') {
      resolve(null); // No preview needed for other formats
      return;
    }

    const previewFilename = filename.replace('.y4m', '_preview.mp4');
    const previewPath = path.join('uploads', previewFilename);

    console.log(`Creating web preview: ${originalPath} -> ${previewPath}`);

    // Convert Y4M to MP4 for web playback (full duration with audio support)
    const ffmpeg = spawn('ffmpeg', [
      '-i', originalPath,
      '-c:v', 'libx264',
      '-preset', 'fast', // Balanced speed/quality for full conversion
      '-crf', '23', // Better quality for original preview
      '-c:a', 'aac', // Audio codec for web compatibility
      '-b:a', '128k', // Audio bitrate
      '-movflags', '+faststart',
      '-y', // Overwrite if exists
      previewPath
    ]);

    let stderr = '';

    ffmpeg.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    ffmpeg.on('close', (code) => {
      if (code === 0) {
        console.log(`Preview created successfully: ${previewPath}`);
        resolve(previewFilename);
      } else {
        console.error(`Preview creation failed with code ${code}`);
        console.error('FFmpeg stderr:', stderr);
        resolve(null); // Don't fail upload if preview fails
      }
    });

    ffmpeg.on('error', (error) => {
      console.error('FFmpeg error:', error);
      resolve(null); // Don't fail upload if preview fails
    });
  });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    // Accept Y4M files and common video formats
    const allowedTypes = ['.y4m', '.mp4', '.avi', '.mov', '.mkv'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Only video files are allowed'), false);
    }
  }
});

// Upload video file
router.post('/upload', upload.single('video'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const isY4M = path.extname(req.file.filename).toLowerCase() === '.y4m';
    let previewFilename = null;
    let videoUrl = `/videos/${req.file.filename}`;

    // Create web preview for Y4M files immediately
    if (isY4M) {
      console.log('Creating preview for Y4M file:', req.file.filename);
      previewFilename = await createWebPreview(req.file.path, req.file.filename);
      if (previewFilename) {
        videoUrl = `/videos/${previewFilename}`; // Use preview as primary URL
        console.log('Preview created:', previewFilename);
      } else {
        console.log('Preview creation failed for:', req.file.filename);
      }
    }

    const videoInfo = {
      filename: req.file.filename,
      originalName: req.file.originalname,
      path: req.file.path,
      size: req.file.size,
      mimetype: req.file.mimetype,
      url: videoUrl, // Use preview URL for Y4M files
      originalUrl: `/videos/${req.file.filename}`, // Keep original URL
      previewUrl: previewFilename ? `/videos/${previewFilename}` : null,
      isY4M: isY4M
    };

    res.json({
      success: true,
      message: 'Video uploaded successfully',
      video: videoInfo
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
});

// Get video from S3
router.post('/s3', async (req, res) => {
  try {
    const { s3Uri } = req.body;
    
    if (!s3Uri) {
      return res.status(400).json({ error: 'S3 URI is required' });
    }

    // Parse S3 URI (s3://bucket/key)
    const s3Match = s3Uri.match(/^s3:\/\/([^\/]+)\/(.+)$/);
    if (!s3Match) {
      return res.status(400).json({ error: 'Invalid S3 URI format' });
    }

    const [, bucket, key] = s3Match;
    
    // Download file from S3
    const localPath = await s3Service.downloadFile(bucket, key);
    
    const videoInfo = {
      filename: path.basename(key),
      originalName: path.basename(key),
      path: localPath,
      s3Uri: s3Uri,
      url: `/videos/${path.basename(localPath)}`
    };

    res.json({
      success: true,
      message: 'Video downloaded from S3 successfully',
      video: videoInfo
    });
  } catch (error) {
    console.error('S3 download error:', error);
    res.status(500).json({ error: 'Failed to download from S3' });
  }
});

// List local videos
router.get('/list', (req, res) => {
  try {
    const uploadsDir = 'uploads';
    const files = fs.readdirSync(uploadsDir)
      .filter(file => {
        const ext = path.extname(file).toLowerCase();
        // Include original video files but exclude preview files
        return ['.y4m', '.mp4', '.avi', '.mov', '.mkv'].includes(ext) &&
               !file.includes('_preview.mp4');
      })
      .map(file => {
        const ext = path.extname(file).toLowerCase();
        const isY4M = ext === '.y4m';

        // Check if preview exists for Y4M files
        let previewUrl = null;
        if (isY4M) {
          const previewFilename = file.replace('.y4m', '_preview.mp4');
          const previewPath = path.join(uploadsDir, previewFilename);
          if (fs.existsSync(previewPath)) {
            previewUrl = `/videos/${previewFilename}`;
          }
        }

        return {
          filename: file,
          path: path.join(uploadsDir, file),
          url: `/videos/${file}`,
          previewUrl: previewUrl,
          isY4M: isY4M,
          size: fs.statSync(path.join(uploadsDir, file)).size
        };
      });

    res.json({
      success: true,
      videos: files
    });
  } catch (error) {
    console.error('List videos error:', error);
    res.status(500).json({ error: 'Failed to list videos' });
  }
});

// Get video metadata
router.get('/metadata/:filename', (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join('uploads', filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'Video not found' });
    }

    const stats = fs.statSync(filePath);
    const metadata = {
      filename: filename,
      path: filePath,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      url: `/videos/${filename}`
    };

    res.json({
      success: true,
      metadata: metadata
    });
  } catch (error) {
    console.error('Metadata error:', error);
    res.status(500).json({ error: 'Failed to get metadata' });
  }
});

// List available directories for batch processing
router.get('/directories', (req, res) => {
  try {
    const baseDir = process.cwd();
    const directories = [];

    // Add common directories that might contain videos
    const commonDirs = ['uploads', 'videos', 'input', 'raw', 'original'];

    commonDirs.forEach(dir => {
      const dirPath = path.join(baseDir, dir);
      if (fs.existsSync(dirPath)) {
        const stats = fs.statSync(dirPath);
        if (stats.isDirectory()) {
          directories.push({
            name: dir,
            path: dirPath,
            relativePath: `./${dir}`,
            exists: true
          });
        }
      } else {
        directories.push({
          name: dir,
          path: dirPath,
          relativePath: `./${dir}`,
          exists: false
        });
      }
    });

    res.json({
      success: true,
      directories: directories,
      baseDir: baseDir
    });
  } catch (error) {
    console.error('List directories error:', error);
    res.status(500).json({ error: 'Failed to list directories' });
  }
});

// Validate directory path
router.post('/validate-path', (req, res) => {
  try {
    const { path: dirPath } = req.body;

    if (!dirPath) {
      return res.status(400).json({ error: 'Path is required' });
    }

    // Check if it's an S3 path
    if (dirPath.startsWith('s3://')) {
      return res.json({
        success: true,
        valid: true,
        type: 's3',
        path: dirPath,
        message: 'S3 path format is valid'
      });
    }

    // For local paths, resolve and check if they exist
    let resolvedPath;
    if (path.isAbsolute(dirPath)) {
      resolvedPath = dirPath;
    } else {
      resolvedPath = path.resolve(process.cwd(), dirPath);
    }

    const exists = fs.existsSync(resolvedPath);
    const isDirectory = exists ? fs.statSync(resolvedPath).isDirectory() : false;

    res.json({
      success: true,
      valid: exists && isDirectory,
      type: 'local',
      path: dirPath,
      resolvedPath: resolvedPath,
      exists: exists,
      isDirectory: isDirectory,
      message: exists && isDirectory ? 'Directory is valid' :
               exists ? 'Path exists but is not a directory' :
               'Directory does not exist'
    });
  } catch (error) {
    console.error('Validate path error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate path',
      details: error.message
    });
  }
});

// Create directory
router.post('/create-directory', (req, res) => {
  try {
    const { path: dirPath } = req.body;

    if (!dirPath) {
      return res.status(400).json({ error: 'Path is required' });
    }

    // Don't try to create S3 directories
    if (dirPath.startsWith('s3://')) {
      return res.json({
        success: true,
        message: 'S3 directories are created automatically'
      });
    }

    // Resolve the path
    let resolvedPath;
    if (path.isAbsolute(dirPath)) {
      resolvedPath = dirPath;
    } else {
      resolvedPath = path.resolve(process.cwd(), dirPath);
    }

    // Create the directory
    fs.mkdirSync(resolvedPath, { recursive: true });

    res.json({
      success: true,
      path: dirPath,
      resolvedPath: resolvedPath,
      message: 'Directory created successfully'
    });
  } catch (error) {
    console.error('Create directory error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create directory',
      details: error.message
    });
  }
});

// List available videos from uploads (for original player)
router.get('/list/uploads', (req, res) => {
  try {
    const videos = [];

    // Scan uploads directory
    const uploadsDir = path.join(process.cwd(), 'uploads');
    if (fs.existsSync(uploadsDir)) {
      const uploadFiles = fs.readdirSync(uploadsDir);
      uploadFiles.forEach(file => {
        const filePath = path.join(uploadsDir, file);
        const stats = fs.statSync(filePath);

        if (stats.isFile() && isVideoFile(file)) {
          const isY4M = file.toLowerCase().endsWith('.y4m');
          let url = `/videos/${file}`;

          // For Y4M files, check if preview exists and use it
          if (isY4M) {
            const previewFile = file.replace('.y4m', '_preview.mp4');
            const previewPath = path.join(uploadsDir, previewFile);
            if (fs.existsSync(previewPath)) {
              url = `/videos/${previewFile}`;
            }
          }

          videos.push({
            filename: file,
            path: filePath,
            url: url,
            size: stats.size,
            type: 'Upload',
            isY4M: isY4M,
            directory: 'uploads'
          });
        }
      });
    }

    res.json({
      success: true,
      videos: videos
    });
  } catch (error) {
    console.error('List uploads error:', error);
    res.status(500).json({ error: 'Failed to list upload videos' });
  }
});

// List available videos from compressed folder (for compressed player)
router.get('/list/compressed', async (req, res) => {
  try {
    const videos = [];

    // Scan local compressed directory
    const compressedDir = path.join(process.cwd(), 'compressed');
    if (fs.existsSync(compressedDir)) {
      const compressedFiles = fs.readdirSync(compressedDir);
      compressedFiles.forEach(file => {
        const filePath = path.join(compressedDir, file);
        const stats = fs.statSync(filePath);

        if (stats.isFile() && isVideoFile(file)) {
          videos.push({
            filename: file,
            path: filePath,
            url: `/compressed/${file}`,
            size: stats.size,
            type: 'Compressed',
            isY4M: file.toLowerCase().endsWith('.y4m'),
            directory: 'compressed'
          });
        }
      });
    }

    // Scan S3 HLS streams
    try {
      const hlsStreams = await getHLSStreams();
      videos.push(...hlsStreams);
    } catch (error) {
      console.error('Failed to list HLS streams:', error);
      // Continue without HLS streams
    }

    res.json({
      success: true,
      videos: videos
    });
  } catch (error) {
    console.error('List compressed videos error:', error);
    res.status(500).json({ error: 'Failed to list compressed videos' });
  }
});

// List all available videos (for general use)
router.get('/list', (req, res) => {
  try {
    const videos = [];

    // Get uploads
    const uploadsResponse = getUploadVideos();
    videos.push(...uploadsResponse);

    // Get compressed
    const compressedResponse = getCompressedVideos();
    videos.push(...compressedResponse);

    res.json({
      success: true,
      videos: videos
    });
  } catch (error) {
    console.error('List videos error:', error);
    res.status(500).json({ error: 'Failed to list videos' });
  }
});

// Helper functions
function getUploadVideos() {
  const videos = [];
  const uploadsDir = path.join(process.cwd(), 'uploads');

  if (fs.existsSync(uploadsDir)) {
    const uploadFiles = fs.readdirSync(uploadsDir);
    uploadFiles.forEach(file => {
      const filePath = path.join(uploadsDir, file);
      const stats = fs.statSync(filePath);

      if (stats.isFile() && isVideoFile(file)) {
        const isY4M = file.toLowerCase().endsWith('.y4m');
        let url = `/videos/${file}`;

        // For Y4M files, check if preview exists and use it
        if (isY4M) {
          const previewFile = file.replace('.y4m', '_preview.mp4');
          const previewPath = path.join(uploadsDir, previewFile);
          if (fs.existsSync(previewPath)) {
            url = `/videos/${previewFile}`;
          }
        }

        videos.push({
          filename: file,
          path: filePath,
          url: url,
          size: stats.size,
          type: 'Upload',
          isY4M: isY4M,
          directory: 'uploads'
        });
      }
    });
  }

  return videos;
}

function getCompressedVideos() {
  const videos = [];
  const compressedDir = path.join(process.cwd(), 'compressed');

  if (fs.existsSync(compressedDir)) {
    const compressedFiles = fs.readdirSync(compressedDir);
    compressedFiles.forEach(file => {
      const filePath = path.join(compressedDir, file);
      const stats = fs.statSync(filePath);

      if (stats.isFile() && isVideoFile(file)) {
        videos.push({
          filename: file,
          path: filePath,
          url: `/compressed/${file}`,
          size: stats.size,
          type: 'Compressed',
          isY4M: file.toLowerCase().endsWith('.y4m'),
          directory: 'compressed'
        });
      }
    });
  }

  return videos;
}

// Helper function to get HLS streams from S3
async function getHLSStreams() {
  const videos = [];

  try {
    // List HLS streams from S3 compressed bucket
    const bucket = 'zmt-compressed-video';
    const prefix = 'hls/';

    const objects = await s3Service.listObjects(bucket, prefix);

    // Group objects by stream directory and find playlists
    const streamDirs = {};

    objects.forEach(obj => {
      const key = obj.key;
      if (key.endsWith('.m3u8')) {
        // This is a playlist file
        const pathParts = key.split('/');
        if (pathParts.length >= 3) { // hls/streamname/playlist.m3u8
          const streamName = pathParts[1];
          const filename = `${streamName}.m3u8`;

          streamDirs[streamName] = {
            filename: filename,
            path: `s3://${bucket}/${key}`,
            url: `/hls/${bucket}/${key}`,
            size: obj.size,
            type: 'HLS Stream',
            isY4M: false,
            directory: 'hls',
            streamName: streamName,
            lastModified: obj.lastModified
          };
        }
      }
    });

    // Convert to array
    videos.push(...Object.values(streamDirs));

    console.log(`Found ${videos.length} HLS streams`);
    return videos;

  } catch (error) {
    console.error('Error listing HLS streams:', error);
    return [];
  }
}

// Route to serve HLS playlists and segments from S3
router.get('/hls/:bucket/:streamName/:file', async (req, res) => {
  try {
    const { bucket, streamName, file } = req.params;
    const key = `hls/${streamName}/${file}`;

    console.log(`Serving HLS file: s3://${bucket}/${key}`);

    // Generate presigned URL for the file
    const presignedUrl = await s3Service.getPresignedUrl(bucket, key, 3600);

    // Set appropriate headers for HLS
    if (file.endsWith('.m3u8')) {
      res.set({
        'Content-Type': 'application/vnd.apple.mpegurl',
        'Cache-Control': 'no-cache',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Range'
      });
    } else if (file.endsWith('.ts')) {
      res.set({
        'Content-Type': 'video/mp2t',
        'Cache-Control': 'max-age=3600',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Range'
      });
    }

    // Redirect to presigned URL
    res.redirect(presignedUrl);

  } catch (error) {
    console.error('HLS serve error:', error);
    res.status(500).json({ error: 'Failed to serve HLS file' });
  }
});

// Helper function to check if file is a video
function isVideoFile(filename) {
  const videoExtensions = ['.y4m', '.mp4', '.avi', '.mov', '.mkv', '.webm', '.m3u8'];
  const ext = path.extname(filename).toLowerCase();
  return videoExtensions.includes(ext);
}

module.exports = router;
