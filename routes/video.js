const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const s3Service = require('../services/s3Service');

const router = express.Router();

// Function to create web-compatible preview for Y4M files
async function createWebPreview(originalPath, filename) {
  return new Promise((resolve, reject) => {
    const ext = path.extname(filename).toLowerCase();

    // Only create preview for Y4M files
    if (ext !== '.y4m') {
      resolve(null); // No preview needed for other formats
      return;
    }

    const previewFilename = filename.replace('.y4m', '_preview.mp4');
    const previewPath = path.join('uploads', previewFilename);

    console.log(`Creating web preview: ${originalPath} -> ${previewPath}`);

    // Convert Y4M to MP4 for web playback (full duration with audio support)
    const ffmpeg = spawn('ffmpeg', [
      '-i', originalPath,
      '-c:v', 'libx264',
      '-preset', 'fast', // Balanced speed/quality for full conversion
      '-crf', '23', // Better quality for original preview
      '-c:a', 'aac', // Audio codec for web compatibility
      '-b:a', '128k', // Audio bitrate
      '-movflags', '+faststart',
      '-y', // Overwrite if exists
      previewPath
    ]);

    let stderr = '';

    ffmpeg.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    ffmpeg.on('close', (code) => {
      if (code === 0) {
        console.log(`Preview created successfully: ${previewPath}`);
        resolve(previewFilename);
      } else {
        console.error(`Preview creation failed with code ${code}`);
        console.error('FFmpeg stderr:', stderr);
        resolve(null); // Don't fail upload if preview fails
      }
    });

    ffmpeg.on('error', (error) => {
      console.error('FFmpeg error:', error);
      resolve(null); // Don't fail upload if preview fails
    });
  });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    // Accept Y4M files and common video formats
    const allowedTypes = ['.y4m', '.mp4', '.avi', '.mov', '.mkv'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Only video files are allowed'), false);
    }
  }
});

// Upload video file
router.post('/upload', upload.single('video'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const isY4M = path.extname(req.file.filename).toLowerCase() === '.y4m';
    let previewFilename = null;
    let videoUrl = `/videos/${req.file.filename}`;

    // Create web preview for Y4M files immediately
    if (isY4M) {
      console.log('Creating preview for Y4M file:', req.file.filename);
      previewFilename = await createWebPreview(req.file.path, req.file.filename);
      if (previewFilename) {
        videoUrl = `/videos/${previewFilename}`; // Use preview as primary URL
        console.log('Preview created:', previewFilename);
      } else {
        console.log('Preview creation failed for:', req.file.filename);
      }
    }

    const videoInfo = {
      filename: req.file.filename,
      originalName: req.file.originalname,
      path: req.file.path,
      size: req.file.size,
      mimetype: req.file.mimetype,
      url: videoUrl, // Use preview URL for Y4M files
      originalUrl: `/videos/${req.file.filename}`, // Keep original URL
      previewUrl: previewFilename ? `/videos/${previewFilename}` : null,
      isY4M: isY4M
    };

    res.json({
      success: true,
      message: 'Video uploaded successfully',
      video: videoInfo
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
});

// Get video from S3
router.post('/s3', async (req, res) => {
  try {
    const { s3Uri } = req.body;
    
    if (!s3Uri) {
      return res.status(400).json({ error: 'S3 URI is required' });
    }

    // Parse S3 URI (s3://bucket/key)
    const s3Match = s3Uri.match(/^s3:\/\/([^\/]+)\/(.+)$/);
    if (!s3Match) {
      return res.status(400).json({ error: 'Invalid S3 URI format' });
    }

    const [, bucket, key] = s3Match;
    
    // Download file from S3
    const localPath = await s3Service.downloadFile(bucket, key);
    
    const videoInfo = {
      filename: path.basename(key),
      originalName: path.basename(key),
      path: localPath,
      s3Uri: s3Uri,
      url: `/videos/${path.basename(localPath)}`
    };

    res.json({
      success: true,
      message: 'Video downloaded from S3 successfully',
      video: videoInfo
    });
  } catch (error) {
    console.error('S3 download error:', error);
    res.status(500).json({ error: 'Failed to download from S3' });
  }
});

// List local videos
router.get('/list', (req, res) => {
  try {
    const uploadsDir = 'uploads';
    const files = fs.readdirSync(uploadsDir)
      .filter(file => {
        const ext = path.extname(file).toLowerCase();
        // Include original video files but exclude preview files
        return ['.y4m', '.mp4', '.avi', '.mov', '.mkv'].includes(ext) &&
               !file.includes('_preview.mp4');
      })
      .map(file => {
        const ext = path.extname(file).toLowerCase();
        const isY4M = ext === '.y4m';

        // Check if preview exists for Y4M files
        let previewUrl = null;
        if (isY4M) {
          const previewFilename = file.replace('.y4m', '_preview.mp4');
          const previewPath = path.join(uploadsDir, previewFilename);
          if (fs.existsSync(previewPath)) {
            previewUrl = `/videos/${previewFilename}`;
          }
        }

        return {
          filename: file,
          path: path.join(uploadsDir, file),
          url: `/videos/${file}`,
          previewUrl: previewUrl,
          isY4M: isY4M,
          size: fs.statSync(path.join(uploadsDir, file)).size
        };
      });

    res.json({
      success: true,
      videos: files
    });
  } catch (error) {
    console.error('List videos error:', error);
    res.status(500).json({ error: 'Failed to list videos' });
  }
});

// Get video metadata
router.get('/metadata/:filename', (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join('uploads', filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'Video not found' });
    }

    const stats = fs.statSync(filePath);
    const metadata = {
      filename: filename,
      path: filePath,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      url: `/videos/${filename}`
    };

    res.json({
      success: true,
      metadata: metadata
    });
  } catch (error) {
    console.error('Metadata error:', error);
    res.status(500).json({ error: 'Failed to get metadata' });
  }
});

// List available directories for batch processing
router.get('/directories', (req, res) => {
  try {
    const baseDir = process.cwd();
    const directories = [];

    // Add common directories that might contain videos
    const commonDirs = ['uploads', 'videos', 'input', 'raw', 'original'];

    commonDirs.forEach(dir => {
      const dirPath = path.join(baseDir, dir);
      if (fs.existsSync(dirPath)) {
        const stats = fs.statSync(dirPath);
        if (stats.isDirectory()) {
          directories.push({
            name: dir,
            path: dirPath,
            relativePath: `./${dir}`,
            exists: true
          });
        }
      } else {
        directories.push({
          name: dir,
          path: dirPath,
          relativePath: `./${dir}`,
          exists: false
        });
      }
    });

    res.json({
      success: true,
      directories: directories,
      baseDir: baseDir
    });
  } catch (error) {
    console.error('List directories error:', error);
    res.status(500).json({ error: 'Failed to list directories' });
  }
});

// Validate directory path
router.post('/validate-path', (req, res) => {
  try {
    const { path: dirPath } = req.body;

    if (!dirPath) {
      return res.status(400).json({ error: 'Path is required' });
    }

    // Check if it's an S3 path
    if (dirPath.startsWith('s3://')) {
      return res.json({
        success: true,
        valid: true,
        type: 's3',
        path: dirPath,
        message: 'S3 path format is valid'
      });
    }

    // For local paths, resolve and check if they exist
    let resolvedPath;
    if (path.isAbsolute(dirPath)) {
      resolvedPath = dirPath;
    } else {
      resolvedPath = path.resolve(process.cwd(), dirPath);
    }

    const exists = fs.existsSync(resolvedPath);
    const isDirectory = exists ? fs.statSync(resolvedPath).isDirectory() : false;

    res.json({
      success: true,
      valid: exists && isDirectory,
      type: 'local',
      path: dirPath,
      resolvedPath: resolvedPath,
      exists: exists,
      isDirectory: isDirectory,
      message: exists && isDirectory ? 'Directory is valid' :
               exists ? 'Path exists but is not a directory' :
               'Directory does not exist'
    });
  } catch (error) {
    console.error('Validate path error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate path',
      details: error.message
    });
  }
});

// Create directory
router.post('/create-directory', (req, res) => {
  try {
    const { path: dirPath } = req.body;

    if (!dirPath) {
      return res.status(400).json({ error: 'Path is required' });
    }

    // Don't try to create S3 directories
    if (dirPath.startsWith('s3://')) {
      return res.json({
        success: true,
        message: 'S3 directories are created automatically'
      });
    }

    // Resolve the path
    let resolvedPath;
    if (path.isAbsolute(dirPath)) {
      resolvedPath = dirPath;
    } else {
      resolvedPath = path.resolve(process.cwd(), dirPath);
    }

    // Create the directory
    fs.mkdirSync(resolvedPath, { recursive: true });

    res.json({
      success: true,
      path: dirPath,
      resolvedPath: resolvedPath,
      message: 'Directory created successfully'
    });
  } catch (error) {
    console.error('Create directory error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create directory',
      details: error.message
    });
  }
});

module.exports = router;
