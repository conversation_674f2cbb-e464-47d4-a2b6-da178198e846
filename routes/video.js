const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const s3Service = require('../services/s3Service');

const router = express.Router();

// Function to create web-compatible preview for Y4M files
async function createWebPreview(originalPath, filename) {
  return new Promise((resolve, reject) => {
    const ext = path.extname(filename).toLowerCase();

    // Only create preview for Y4M files
    if (ext !== '.y4m') {
      resolve(null); // No preview needed for other formats
      return;
    }

    const previewFilename = filename.replace('.y4m', '_preview.mp4');
    const previewPath = path.join('uploads', previewFilename);

    console.log(`Creating web preview: ${originalPath} -> ${previewPath}`);

    // Convert Y4M to MP4 for web playback (full duration with audio support)
    const ffmpeg = spawn('ffmpeg', [
      '-i', originalPath,
      '-c:v', 'libx264',
      '-preset', 'fast', // Balanced speed/quality for full conversion
      '-crf', '23', // Better quality for original preview
      '-c:a', 'aac', // Audio codec for web compatibility
      '-b:a', '128k', // Audio bitrate
      '-movflags', '+faststart',
      '-y', // Overwrite if exists
      previewPath
    ]);

    let stderr = '';

    ffmpeg.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    ffmpeg.on('close', (code) => {
      if (code === 0) {
        console.log(`Preview created successfully: ${previewPath}`);
        resolve(previewFilename);
      } else {
        console.error(`Preview creation failed with code ${code}`);
        console.error('FFmpeg stderr:', stderr);
        resolve(null); // Don't fail upload if preview fails
      }
    });

    ffmpeg.on('error', (error) => {
      console.error('FFmpeg error:', error);
      resolve(null); // Don't fail upload if preview fails
    });
  });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    // Accept Y4M files and common video formats
    const allowedTypes = ['.y4m', '.mp4', '.avi', '.mov', '.mkv'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Only video files are allowed'), false);
    }
  }
});

// Upload video file
router.post('/upload', upload.single('video'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Create web preview for Y4M files
    const previewFilename = await createWebPreview(req.file.path, req.file.filename);

    const videoInfo = {
      filename: req.file.filename,
      originalName: req.file.originalname,
      path: req.file.path,
      size: req.file.size,
      mimetype: req.file.mimetype,
      url: `/videos/${req.file.filename}`,
      previewUrl: previewFilename ? `/videos/${previewFilename}` : null,
      isY4M: path.extname(req.file.filename).toLowerCase() === '.y4m'
    };

    res.json({
      success: true,
      message: 'Video uploaded successfully',
      video: videoInfo
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
});

// Get video from S3
router.post('/s3', async (req, res) => {
  try {
    const { s3Uri } = req.body;
    
    if (!s3Uri) {
      return res.status(400).json({ error: 'S3 URI is required' });
    }

    // Parse S3 URI (s3://bucket/key)
    const s3Match = s3Uri.match(/^s3:\/\/([^\/]+)\/(.+)$/);
    if (!s3Match) {
      return res.status(400).json({ error: 'Invalid S3 URI format' });
    }

    const [, bucket, key] = s3Match;
    
    // Download file from S3
    const localPath = await s3Service.downloadFile(bucket, key);
    
    const videoInfo = {
      filename: path.basename(key),
      originalName: path.basename(key),
      path: localPath,
      s3Uri: s3Uri,
      url: `/videos/${path.basename(localPath)}`
    };

    res.json({
      success: true,
      message: 'Video downloaded from S3 successfully',
      video: videoInfo
    });
  } catch (error) {
    console.error('S3 download error:', error);
    res.status(500).json({ error: 'Failed to download from S3' });
  }
});

// List local videos
router.get('/list', (req, res) => {
  try {
    const uploadsDir = 'uploads';
    const files = fs.readdirSync(uploadsDir)
      .filter(file => {
        const ext = path.extname(file).toLowerCase();
        // Include original video files but exclude preview files
        return ['.y4m', '.mp4', '.avi', '.mov', '.mkv'].includes(ext) &&
               !file.includes('_preview.mp4');
      })
      .map(file => {
        const ext = path.extname(file).toLowerCase();
        const isY4M = ext === '.y4m';

        // Check if preview exists for Y4M files
        let previewUrl = null;
        if (isY4M) {
          const previewFilename = file.replace('.y4m', '_preview.mp4');
          const previewPath = path.join(uploadsDir, previewFilename);
          if (fs.existsSync(previewPath)) {
            previewUrl = `/videos/${previewFilename}`;
          }
        }

        return {
          filename: file,
          path: path.join(uploadsDir, file),
          url: `/videos/${file}`,
          previewUrl: previewUrl,
          isY4M: isY4M,
          size: fs.statSync(path.join(uploadsDir, file)).size
        };
      });

    res.json({
      success: true,
      videos: files
    });
  } catch (error) {
    console.error('List videos error:', error);
    res.status(500).json({ error: 'Failed to list videos' });
  }
});

// Get video metadata
router.get('/metadata/:filename', (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join('uploads', filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'Video not found' });
    }

    const stats = fs.statSync(filePath);
    const metadata = {
      filename: filename,
      path: filePath,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      url: `/videos/${filename}`
    };

    res.json({
      success: true,
      metadata: metadata
    });
  } catch (error) {
    console.error('Metadata error:', error);
    res.status(500).json({ error: 'Failed to get metadata' });
  }
});

module.exports = router;
