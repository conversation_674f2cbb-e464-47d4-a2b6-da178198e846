const express = require('express');
const compressionService = require('../services/compressionService');
const path = require('path');
const fs = require('fs');

const router = express.Router();

// Start compression process
router.post('/start', async (req, res) => {
  try {
    const { inputPath, outputPath, isS3, mode = 'single' } = req.body;

    if (!inputPath) {
      return res.status(400).json({ error: 'Input path is required' });
    }

    if (mode === 'batch' && !outputPath) {
      return res.status(400).json({ error: 'Output path is required for batch mode' });
    }

    // Validate input path exists (for local files in single mode)
    if (mode === 'single' && !isS3 && !fs.existsSync(inputPath)) {
      return res.status(404).json({ error: 'Input file not found' });
    }

    // Start compression process
    const compressionId = await compressionService.startCompression(
      inputPath,
      req.io,
      isS3,
      mode,
      outputPath
    );

    res.json({
      success: true,
      message: mode === 'batch' ? 'Batch compression started' : 'Compression started',
      compressionId: compressionId
    });
  } catch (error) {
    console.error('Compression start error:', error);
    res.status(500).json({ error: 'Failed to start compression' });
  }
});

// Get compression status
router.get('/status/:id', (req, res) => {
  try {
    const { id } = req.params;
    const status = compressionService.getCompressionStatus(id);
    
    if (!status) {
      return res.status(404).json({ error: 'Compression job not found' });
    }

    res.json({
      success: true,
      status: status
    });
  } catch (error) {
    console.error('Status check error:', error);
    res.status(500).json({ error: 'Failed to get status' });
  }
});

// List compressed videos
router.get('/list', (req, res) => {
  try {
    const compressedDir = 'compressed';

    if (!fs.existsSync(compressedDir)) {
      return res.json({
        success: true,
        videos: []
      });
    }

    const files = fs.readdirSync(compressedDir)
      .filter(file => {
        const ext = path.extname(file).toLowerCase();
        // Include Y4M, MP4, and other video formats, but exclude preview files
        return ['.y4m', '.mp4', '.webm', '.avi', '.mov', '.mkv'].includes(ext) &&
               !file.includes('_preview.mp4');
      })
      .map(file => {
        const filePath = path.join(compressedDir, file);
        const stats = fs.statSync(filePath);
        const ext = path.extname(file).toLowerCase();
        const isY4M = ext === '.y4m';

        // Check if preview exists for Y4M files
        let previewUrl = null;
        if (isY4M) {
          const previewFilename = file.replace('.y4m', '_preview.mp4');
          const previewPath = path.join(compressedDir, previewFilename);
          if (fs.existsSync(previewPath)) {
            previewUrl = `/compressed/${previewFilename}`;
          }
        }

        return {
          filename: file,
          path: filePath,
          url: `/compressed/${file}`,
          previewUrl: previewUrl,
          isY4M: isY4M,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created)); // Sort by newest first

    res.json({
      success: true,
      videos: files
    });
  } catch (error) {
    console.error('List compressed videos error:', error);
    res.status(500).json({ error: 'Failed to list compressed videos' });
  }
});

// Cancel compression
router.post('/cancel/:id', (req, res) => {
  try {
    const { id } = req.params;
    const success = compressionService.cancelCompression(id);
    
    if (!success) {
      return res.status(404).json({ error: 'Compression job not found' });
    }

    res.json({
      success: true,
      message: 'Compression cancelled'
    });
  } catch (error) {
    console.error('Cancel compression error:', error);
    res.status(500).json({ error: 'Failed to cancel compression' });
  }
});

// Get compression logs
router.get('/logs/:id', (req, res) => {
  try {
    const { id } = req.params;
    const logs = compressionService.getCompressionLogs(id);

    res.json({
      success: true,
      logs: logs
    });
  } catch (error) {
    console.error('Get logs error:', error);
    res.status(500).json({ error: 'Failed to get logs' });
  }
});

// Create preview for compressed Y4M file
router.post('/create-preview', async (req, res) => {
  try {
    const { filename } = req.body;

    if (!filename) {
      return res.status(400).json({ error: 'Filename is required' });
    }

    const compressedPath = path.join('compressed', filename);

    // Check if file exists and is Y4M
    if (!fs.existsSync(compressedPath)) {
      return res.status(404).json({ error: 'Compressed file not found' });
    }

    const ext = path.extname(filename).toLowerCase();
    if (ext !== '.y4m') {
      return res.status(400).json({ error: 'Preview only needed for Y4M files' });
    }

    // Create preview filename
    const previewFilename = filename.replace('.y4m', '_preview.mp4');
    const previewPath = path.join('compressed', previewFilename);

    // Check if preview already exists
    if (fs.existsSync(previewPath)) {
      return res.json({
        success: true,
        message: 'Preview already exists',
        previewFilename: previewFilename
      });
    }

    // Create preview using FFmpeg
    const { spawn } = require('child_process');

    const ffmpeg = spawn('ffmpeg', [
      '-i', compressedPath,
      '-c:v', 'libx264',
      '-preset', 'ultrafast',
      '-crf', '28',
      '-movflags', '+faststart',
      '-t', '60', // Limit to 60 seconds for compressed previews
      '-y',
      previewPath
    ]);

    ffmpeg.on('close', (code) => {
      if (code === 0) {
        console.log(`Compressed preview created: ${previewPath}`);
      } else {
        console.error(`Preview creation failed with code ${code}`);
      }
    });

    ffmpeg.on('error', (error) => {
      console.error('FFmpeg error:', error);
    });

    res.json({
      success: true,
      message: 'Preview creation started',
      previewFilename: previewFilename
    });

  } catch (error) {
    console.error('Create preview error:', error);
    res.status(500).json({ error: 'Failed to create preview' });
  }
});

module.exports = router;
