const express = require('express');
const compressionService = require('../services/compressionService');
const path = require('path');
const fs = require('fs');

const router = express.Router();

// Start compression process
router.post('/start', async (req, res) => {
  try {
    const { inputPath, outputPath, mode = 'single', inputRegion, outputRegion } = req.body;

    if (!inputPath) {
      return res.status(400).json({ error: 'Input path is required' });
    }

    if (mode === 'batch' && !outputPath) {
      return res.status(400).json({ error: 'Output path is required for batch mode' });
    }



    console.log('Starting compression with params:', {
      inputPath,
      outputPath,
      mode,
      inputRegion,
      outputRegion
    });

    // Start compression process with correct parameter order
    const compressionId = await compressionService.startCompression(
      inputPath,
      outputPath,
      mode,
      inputRegion,
      outputRegion,
      req.io
    );

    res.json({
      success: true,
      message: mode === 'batch' ? 'Batch compression started' : 'Compression started',
      compressionId: compressionId
    });
  } catch (error) {
    console.error('Compression start error:', error);
    res.status(500).json({ error: 'Failed to start compression' });
  }
});

// Get compression status
router.get('/status/:id', (req, res) => {
  try {
    const { id } = req.params;
    const status = compressionService.getCompressionStatus(id);
    
    if (!status) {
      return res.status(404).json({ error: 'Compression job not found' });
    }

    res.json({
      success: true,
      status: status
    });
  } catch (error) {
    console.error('Status check error:', error);
    res.status(500).json({ error: 'Failed to get status' });
  }
});

// List compressed videos
router.get('/list', (req, res) => {
  try {
    const compressedDir = 'compressed';

    if (!fs.existsSync(compressedDir)) {
      return res.json({
        success: true,
        videos: []
      });
    }

    const files = fs.readdirSync(compressedDir)
      .filter(file => {
        const ext = path.extname(file).toLowerCase();
        // Include Y4M, MP4, and other video formats, but exclude preview files
        return ['.y4m', '.mp4', '.webm', '.avi', '.mov', '.mkv'].includes(ext) &&
               !file.includes('_preview.mp4');
      })
      .map(file => {
        const filePath = path.join(compressedDir, file);
        const stats = fs.statSync(filePath);
        const ext = path.extname(file).toLowerCase();
        const isY4M = ext === '.y4m';

        // Check if preview exists for Y4M files
        let previewUrl = null;
        if (isY4M) {
          const previewFilename = file.replace('.y4m', '_preview.mp4');
          const previewPath = path.join(compressedDir, previewFilename);
          if (fs.existsSync(previewPath)) {
            previewUrl = `/compressed/${previewFilename}`;
          }
        }

        return {
          filename: file,
          path: filePath,
          url: `/compressed/${file}`,
          previewUrl: previewUrl,
          isY4M: isY4M,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created)); // Sort by newest first

    res.json({
      success: true,
      videos: files
    });
  } catch (error) {
    console.error('List compressed videos error:', error);
    res.status(500).json({ error: 'Failed to list compressed videos' });
  }
});

// Cancel compression
router.post('/cancel/:id', (req, res) => {
  try {
    const { id } = req.params;
    const success = compressionService.cancelCompression(id);
    
    if (!success) {
      return res.status(404).json({ error: 'Compression job not found' });
    }

    res.json({
      success: true,
      message: 'Compression cancelled'
    });
  } catch (error) {
    console.error('Cancel compression error:', error);
    res.status(500).json({ error: 'Failed to cancel compression' });
  }
});

// Get compression logs
router.get('/logs/:id', (req, res) => {
  try {
    const { id } = req.params;
    const logs = compressionService.getCompressionLogs(id);

    res.json({
      success: true,
      logs: logs
    });
  } catch (error) {
    console.error('Get logs error:', error);
    res.status(500).json({ error: 'Failed to get logs' });
  }
});

// Create preview for compressed Y4M file
router.post('/create-preview', async (req, res) => {
  try {
    const { filename } = req.body;

    if (!filename) {
      return res.status(400).json({ error: 'Filename is required' });
    }

    const compressedPath = path.join('compressed', filename);

    // Check if file exists and is Y4M
    if (!fs.existsSync(compressedPath)) {
      return res.status(404).json({ error: 'Compressed file not found' });
    }

    const ext = path.extname(filename).toLowerCase();
    if (ext !== '.y4m') {
      return res.status(400).json({ error: 'Preview only needed for Y4M files' });
    }

    // Create preview filename
    const previewFilename = filename.replace('.y4m', '_preview.mp4');
    const previewPath = path.join('compressed', previewFilename);

    // Check if preview already exists
    if (fs.existsSync(previewPath)) {
      return res.json({
        success: true,
        message: 'Preview already exists',
        previewFilename: previewFilename
      });
    }

    // Create preview using FFmpeg
    const { spawn } = require('child_process');

    const ffmpeg = spawn('ffmpeg', [
      '-i', compressedPath,
      '-c:v', 'libx264',
      '-preset', 'ultrafast',
      '-crf', '28',
      '-c:a', 'aac', // Audio codec
      '-b:a', '128k', // Audio bitrate
      '-movflags', '+faststart',
      '-t', '120', // Limit to 2 minutes for compressed previews (longer than original)
      '-y',
      previewPath
    ]);

    ffmpeg.on('close', (code) => {
      if (code === 0) {
        console.log(`Compressed preview created: ${previewPath}`);
      } else {
        console.error(`Preview creation failed with code ${code}`);
      }
    });

    ffmpeg.on('error', (error) => {
      console.error('FFmpeg error:', error);
    });

    res.json({
      success: true,
      message: 'Preview creation started',
      previewFilename: previewFilename
    });

  } catch (error) {
    console.error('Create preview error:', error);
    res.status(500).json({ error: 'Failed to create preview' });
  }
});

// Get file information (size, etc.)
router.get('/file-info/:filename', async (req, res) => {
  try {
    const { filename } = req.params;

    // Check if it's an HLS stream (.m3u8 file)
    if (filename.endsWith('.m3u8')) {
      try {
        const hlsSize = await getHLSStreamSize(filename);
        res.json({
          success: true,
          filename: filename,
          size: hlsSize.totalSize,
          segmentCount: hlsSize.segmentCount,
          type: 'HLS Stream',
          path: `HLS Stream (${hlsSize.segmentCount} segments)`
        });
        return;
      } catch (error) {
        console.error('Failed to get HLS stream size:', error);
        return res.status(500).json({ error: 'Failed to get HLS stream information' });
      }
    }

    // Handle regular files in compressed directory
    const filePath = path.join('compressed', filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    const stats = fs.statSync(filePath);

    res.json({
      success: true,
      filename: filename,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      path: filePath
    });
  } catch (error) {
    console.error('Get file info error:', error);
    res.status(500).json({ error: 'Failed to get file information' });
  }
});

// Helper function to calculate total size of HLS stream
async function getHLSStreamSize(filename) {
  const s3Service = require('../services/s3Service');
  const bucket = 'zmt-compressed-video';

  // Find the stream directory based on filename
  let streamKey = null;
  let streamPrefix = null;

  // Check different possible locations for the .m3u8 file
  const possiblePrefixes = [
    `hls/${filename.replace('.m3u8', '')}/`,
    `output/${filename.replace('.m3u8', '')}/`,
    'output/',
    'hls/'
  ];

  for (const prefix of possiblePrefixes) {
    try {
      const objects = await s3Service.listObjects(bucket, prefix);
      const playlistFile = objects.find(obj => obj.key.endsWith('playlist.m3u8') || obj.key.endsWith(filename));

      if (playlistFile) {
        streamKey = playlistFile.key;
        streamPrefix = prefix;
        break;
      }
    } catch (error) {
      console.log(`No objects found in prefix: ${prefix}`);
      continue;
    }
  }

  if (!streamKey) {
    throw new Error(`HLS stream not found for filename: ${filename}`);
  }

  // Get all objects in the stream directory
  const streamDir = streamKey.substring(0, streamKey.lastIndexOf('/') + 1);
  const allObjects = await s3Service.listObjects(bucket, streamDir);

  // Calculate total size of all segments and playlist files
  let totalSize = 0;
  let segmentCount = 0;

  allObjects.forEach(obj => {
    totalSize += obj.size || 0;
    if (obj.key.endsWith('.ts')) {
      segmentCount++;
    }
  });

  console.log(`HLS Stream ${filename}: ${segmentCount} segments, total size: ${totalSize} bytes`);

  return {
    totalSize: totalSize,
    segmentCount: segmentCount,
    streamDirectory: streamDir
  };
}

module.exports = router;
