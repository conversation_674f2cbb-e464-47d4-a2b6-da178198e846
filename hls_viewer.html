<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure HLS Video Player</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .video-container {
            margin-bottom: 30px;
            text-align: center;
        }
        
        video {
            width: 100%;
            max-width: 800px;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        
        .url-input {
            width: 70%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            margin-right: 10px;
        }
        
        .load-btn {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .load-btn:hover {
            background-color: #0056b3;
        }
        
        .load-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .info-panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #6c757d;
            font-family: monospace;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .status.loading {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .file-upload {
            margin: 20px 0;
            text-align: center;
        }
        
        .file-input {
            margin: 10px;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 6px;
            background-color: #fafafa;
        }
        
        .example-urls {
            background-color: #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .example-urls h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .example-url {
            background-color: white;
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            cursor: pointer;
            border: 1px solid #ddd;
            transition: background-color 0.3s;
        }
        
        .example-url:hover {
            background-color: #f8f9fa;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
</head>
<body>
    <div class="container">
        <h1>🎬 Secure HLS Video Player</h1>
        <p style="text-align: center; color: #666;">
            Stream HLS videos using presigned URLs from S3
        </p>
        
        <div class="video-container">
            <video id="video" controls muted>
                Your browser does not support the video tag.
            </video>
        </div>
        
        <div class="controls">
            <input type="text" id="urlInput" class="url-input" 
                   placeholder="Enter presigned playlist URL or upload presigned playlist file">
            <button id="loadBtn" class="load-btn" onclick="loadVideo()">Load Video</button>
        </div>
        
        <div class="file-upload">
            <div class="file-input">
                <label for="fileInput">📁 Or upload presigned playlist file (.m3u8):</label><br>
                <input type="file" id="fileInput" accept=".m3u8" onchange="loadFromFile()">
            </div>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="info-panel">
            <h3>📊 Video Information</h3>
            <div class="info-row">
                <span class="info-label">Status:</span>
                <span class="info-value" id="videoStatus">Not loaded</span>
            </div>
            <div class="info-row">
                <span class="info-label">Duration:</span>
                <span class="info-value" id="videoDuration">-</span>
            </div>
            <div class="info-row">
                <span class="info-label">Current Time:</span>
                <span class="info-value" id="currentTime">-</span>
            </div>
            <div class="info-row">
                <span class="info-label">Playlist URL:</span>
                <span class="info-value" id="playlistUrl">-</span>
            </div>
            <div class="info-row">
                <span class="info-label">HLS Support:</span>
                <span class="info-value" id="hlsSupport">-</span>
            </div>
        </div>
        
        <div class="example-urls">
            <h3>💡 How to use:</h3>
            <ol>
                <li><strong>Run HLS streaming:</strong> Use the updated <code>hls_streaming_s3.py</code> script</li>
                <li><strong>Get presigned URL:</strong> The script will generate presigned URLs automatically</li>
                <li><strong>Load video:</strong> Paste the presigned playlist URL above or upload the presigned playlist file</li>
                <li><strong>Stream securely:</strong> All segments use presigned URLs for secure access</li>
            </ol>
            
            <p><strong>Example presigned URL format:</strong></p>
            <div class="example-url">
                https://zmt-compressed-video.s3.us-west-1.amazonaws.com/hls/session/playlist.m3u8?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=...
            </div>
        </div>
    </div>

    <script>
        const video = document.getElementById('video');
        const urlInput = document.getElementById('urlInput');
        const loadBtn = document.getElementById('loadBtn');
        const fileInput = document.getElementById('fileInput');
        const status = document.getElementById('status');
        
        let hls;
        
        // Check HLS support
        function checkHLSSupport() {
            const hlsSupported = Hls.isSupported();
            const nativeSupported = video.canPlayType('application/vnd.apple.mpegurl');
            
            document.getElementById('hlsSupport').textContent = 
                hlsSupported ? 'HLS.js supported' : 
                nativeSupported ? 'Native HLS supported' : 'Not supported';
            
            return hlsSupported || nativeSupported;
        }
        
        // Show status message
        function showStatus(message, type) {
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    status.style.display = 'none';
                }, 5000);
            }
        }
        
        // Update video information
        function updateVideoInfo() {
            document.getElementById('videoStatus').textContent = video.paused ? 'Paused' : 'Playing';
            document.getElementById('videoDuration').textContent = 
                video.duration ? `${video.duration.toFixed(1)}s` : '-';
            document.getElementById('currentTime').textContent = 
                `${video.currentTime.toFixed(1)}s`;
        }
        
        // Load video from URL
        function loadVideo() {
            const url = urlInput.value.trim();
            
            if (!url) {
                showStatus('Please enter a playlist URL', 'error');
                return;
            }
            
            loadVideoFromUrl(url);
        }
        
        // Load video from file
        function loadFromFile() {
            const file = fileInput.files[0];
            
            if (!file) {
                return;
            }
            
            if (!file.name.endsWith('.m3u8')) {
                showStatus('Please select a .m3u8 playlist file', 'error');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                
                // Create a blob URL for the playlist content
                const blob = new Blob([content], { type: 'application/vnd.apple.mpegurl' });
                const blobUrl = URL.createObjectURL(blob);
                
                loadVideoFromUrl(blobUrl, true);
            };
            reader.readAsText(file);
        }
        
        // Load video from URL or blob
        function loadVideoFromUrl(url, isLocal = false) {
            showStatus('Loading video...', 'loading');
            loadBtn.disabled = true;
            
            // Clean up previous HLS instance
            if (hls) {
                hls.destroy();
            }
            
            // Update playlist URL display
            document.getElementById('playlistUrl').textContent = 
                isLocal ? 'Local file' : url.substring(0, 100) + (url.length > 100 ? '...' : '');
            
            if (Hls.isSupported()) {
                // Use HLS.js
                hls = new Hls({
                    debug: false,
                    enableWorker: true
                });
                
                hls.loadSource(url);
                hls.attachMedia(video);
                
                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    showStatus('Video loaded successfully!', 'success');
                    loadBtn.disabled = false;
                    video.play().catch(e => {
                        console.log('Autoplay prevented:', e);
                    });
                });
                
                hls.on(Hls.Events.ERROR, function(event, data) {
                    console.error('HLS Error:', data);
                    showStatus(`Error loading video: ${data.details}`, 'error');
                    loadBtn.disabled = false;
                });
                
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                // Use native HLS support (Safari)
                video.src = url;
                video.addEventListener('loadedmetadata', function() {
                    showStatus('Video loaded successfully!', 'success');
                    loadBtn.disabled = false;
                });
                
                video.addEventListener('error', function() {
                    showStatus('Error loading video', 'error');
                    loadBtn.disabled = false;
                });
                
            } else {
                showStatus('HLS not supported in this browser', 'error');
                loadBtn.disabled = false;
            }
        }
        
        // Video event listeners
        video.addEventListener('timeupdate', updateVideoInfo);
        video.addEventListener('play', updateVideoInfo);
        video.addEventListener('pause', updateVideoInfo);
        video.addEventListener('loadedmetadata', updateVideoInfo);
        
        // Enter key support for URL input
        urlInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loadVideo();
            }
        });
        
        // Initialize
        checkHLSSupport();
        updateVideoInfo();
    </script>
</body>
</html>
