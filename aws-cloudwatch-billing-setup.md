# AWS CloudWatch and Billing Integration

## 1. CloudWatch Metrics for S3

### Enable S3 Request Metrics
```bash
# Enable request metrics for compressed video bucket
aws s3api put-bucket-metrics-configuration \
    --bucket zmt-compressed-video \
    --id EntireBucket \
    --metrics-configuration '{
        "Id": "EntireBucket",
        "Filter": {
            "Prefix": ""
        }
    }'

# Enable request metrics for source video bucket
aws s3api put-bucket-metrics-configuration \
    --bucket zmt-source-video \
    --id EntireBucket \
    --metrics-configuration '{
        "Id": "EntireBucket",
        "Filter": {
            "Prefix": ""
        }
    }'
```

### CloudWatch Custom Metrics for HLS Streaming
```python
#!/usr/bin/env python3
import boto3
from datetime import datetime

def publish_hls_metrics(upload_stats):
    """
    Publish custom CloudWatch metrics for HLS streaming
    """
    cloudwatch = boto3.client('cloudwatch')
    
    # Publish metrics
    metrics = [
        {
            'MetricName': 'FilesUploaded',
            'Value': upload_stats['total_files'],
            'Unit': 'Count',
            'Dimensions': [
                {'Name': 'StreamType', 'Value': 'HLS'},
                {'Name': 'Bucket', 'Value': 'zmt-compressed-video'}
            ]
        },
        {
            'MetricName': 'BytesUploaded',
            'Value': upload_stats['total_bytes'],
            'Unit': 'Bytes',
            'Dimensions': [
                {'Name': 'StreamType', 'Value': 'HLS'},
                {'Name': 'Bucket', 'Value': 'zmt-compressed-video'}
            ]
        },
        {
            'MetricName': 'UploadSpeed',
            'Value': upload_stats['average_speed_mbps'],
            'Unit': 'Count/Second',
            'Dimensions': [
                {'Name': 'StreamType', 'Value': 'HLS'},
                {'Name': 'Bucket', 'Value': 'zmt-compressed-video'}
            ]
        },
        {
            'MetricName': 'SessionDuration',
            'Value': upload_stats['session_duration_seconds'],
            'Unit': 'Seconds',
            'Dimensions': [
                {'Name': 'StreamType', 'Value': 'HLS'},
                {'Name': 'Bucket', 'Value': 'zmt-compressed-video'}
            ]
        }
    ]
    
    # Send metrics to CloudWatch
    cloudwatch.put_metric_data(
        Namespace='HLS/Streaming',
        MetricData=metrics
    )
    
    print(f"Published {len(metrics)} metrics to CloudWatch")

# Integration with HLS streaming script
def enhanced_upload_tracker_with_cloudwatch():
    """
    Enhanced upload tracker that publishes to CloudWatch
    """
    # ... existing upload tracking code ...
    
    # At the end of streaming session
    summary = upload_tracker.get_summary()
    publish_hls_metrics(summary)
```

## 2. CloudWatch Dashboards

### Create S3 Bandwidth Dashboard
```python
import boto3
import json

def create_s3_bandwidth_dashboard():
    """
    Create CloudWatch dashboard for S3 bandwidth monitoring
    """
    cloudwatch = boto3.client('cloudwatch')
    
    dashboard_body = {
        "widgets": [
            {
                "type": "metric",
                "x": 0, "y": 0,
                "width": 12, "height": 6,
                "properties": {
                    "metrics": [
                        ["AWS/S3", "BucketSizeBytes", "BucketName", "zmt-compressed-video", "StorageType", "StandardStorage"],
                        [".", ".", ".", "zmt-source-video", ".", "."]
                    ],
                    "period": 86400,
                    "stat": "Average",
                    "region": "us-west-1",
                    "title": "S3 Bucket Size",
                    "yAxis": {"left": {"min": 0}}
                }
            },
            {
                "type": "metric",
                "x": 12, "y": 0,
                "width": 12, "height": 6,
                "properties": {
                    "metrics": [
                        ["AWS/S3", "NumberOfObjects", "BucketName", "zmt-compressed-video", "StorageType", "AllStorageTypes"],
                        [".", ".", ".", "zmt-source-video", ".", "."]
                    ],
                    "period": 86400,
                    "stat": "Average",
                    "region": "us-west-1",
                    "title": "Number of Objects",
                    "yAxis": {"left": {"min": 0}}
                }
            },
            {
                "type": "metric",
                "x": 0, "y": 6,
                "width": 8, "height": 6,
                "properties": {
                    "metrics": [
                        ["AWS/S3", "AllRequests", "BucketName", "zmt-compressed-video"],
                        [".", "4xxErrors", ".", "."],
                        [".", "5xxErrors", ".", "."]
                    ],
                    "period": 300,
                    "stat": "Sum",
                    "region": "us-west-1",
                    "title": "S3 Requests",
                    "yAxis": {"left": {"min": 0}}
                }
            },
            {
                "type": "metric",
                "x": 8, "y": 6,
                "width": 8, "height": 6,
                "properties": {
                    "metrics": [
                        ["AWS/S3", "BytesDownloaded", "BucketName", "zmt-compressed-video"],
                        [".", "BytesUploaded", ".", "."]
                    ],
                    "period": 300,
                    "stat": "Sum",
                    "region": "us-west-1",
                    "title": "Data Transfer",
                    "yAxis": {"left": {"min": 0}}
                }
            },
            {
                "type": "metric",
                "x": 16, "y": 6,
                "width": 8, "height": 6,
                "properties": {
                    "metrics": [
                        ["HLS/Streaming", "FilesUploaded", "StreamType", "HLS"],
                        [".", "BytesUploaded", ".", "."],
                        [".", "UploadSpeed", ".", "."]
                    ],
                    "period": 300,
                    "stat": "Average",
                    "region": "us-west-1",
                    "title": "HLS Streaming Metrics",
                    "yAxis": {"left": {"min": 0}}
                }
            }
        ]
    }
    
    # Create dashboard
    cloudwatch.put_dashboard(
        DashboardName='S3-Bandwidth-Monitoring',
        DashboardBody=json.dumps(dashboard_body)
    )
    
    print("Created S3 Bandwidth Monitoring Dashboard")

# Create the dashboard
create_s3_bandwidth_dashboard()
```

## 3. AWS Cost and Billing Integration

### Enable Cost and Usage Reports
```bash
# Create S3 bucket for cost reports
aws s3 mb s3://zmt-cost-reports --region us-east-1

# Set bucket policy for Cost and Usage Reports
aws s3api put-bucket-policy --bucket zmt-cost-reports --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "billingreports.amazonaws.com"
      },
      "Action": [
        "s3:GetBucketAcl",
        "s3:GetBucketPolicy"
      ],
      "Resource": "arn:aws:s3:::zmt-cost-reports"
    },
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "billingreports.amazonaws.com"
      },
      "Action": "s3:PutObject",
      "Resource": "arn:aws:s3:::zmt-cost-reports/*"
    }
  ]
}'
```

### Cost Analysis Script
```python
#!/usr/bin/env python3
import boto3
from datetime import datetime, timedelta
import json

def analyze_s3_costs(start_date, end_date):
    """
    Analyze S3 costs using AWS Cost Explorer API
    """
    ce = boto3.client('ce')
    
    # Get cost and usage data
    response = ce.get_cost_and_usage(
        TimePeriod={
            'Start': start_date.strftime('%Y-%m-%d'),
            'End': end_date.strftime('%Y-%m-%d')
        },
        Granularity='DAILY',
        Metrics=['BlendedCost', 'UsageQuantity'],
        GroupBy=[
            {'Type': 'DIMENSION', 'Key': 'SERVICE'},
            {'Type': 'DIMENSION', 'Key': 'USAGE_TYPE'}
        ],
        Filter={
            'Dimensions': {
                'Key': 'SERVICE',
                'Values': ['Amazon Simple Storage Service']
            }
        }
    )
    
    # Process results
    costs_by_day = {}
    total_cost = 0
    
    for result in response['ResultsByTime']:
        date = result['TimePeriod']['Start']
        daily_cost = 0
        
        for group in result['Groups']:
            service = group['Keys'][0]
            usage_type = group['Keys'][1]
            cost = float(group['Metrics']['BlendedCost']['Amount'])
            usage = float(group['Metrics']['UsageQuantity']['Amount'])
            
            daily_cost += cost
            
            if date not in costs_by_day:
                costs_by_day[date] = {'total': 0, 'breakdown': {}}
            
            costs_by_day[date]['total'] += cost
            costs_by_day[date]['breakdown'][usage_type] = {
                'cost': cost,
                'usage': usage
            }
        
        total_cost += daily_cost
    
    return {
        'total_cost': total_cost,
        'daily_costs': costs_by_day,
        'period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"
    }

def generate_cost_report(cost_data):
    """
    Generate comprehensive cost report
    """
    print("="*80)
    print("AWS S3 COST ANALYSIS REPORT")
    print("="*80)
    
    print(f"Period: {cost_data['period']}")
    print(f"Total Cost: ${cost_data['total_cost']:.4f}")
    
    print("\nDaily Cost Breakdown:")
    for date, data in sorted(cost_data['daily_costs'].items()):
        print(f"\n{date}: ${data['total']:.4f}")
        
        # Show top cost components
        sorted_breakdown = sorted(data['breakdown'].items(), 
                                key=lambda x: x[1]['cost'], reverse=True)
        
        for usage_type, metrics in sorted_breakdown[:5]:  # Top 5
            print(f"  {usage_type}: ${metrics['cost']:.4f} ({metrics['usage']:.2f} units)")

def estimate_bandwidth_costs(bytes_transferred, region='us-west-1'):
    """
    Estimate bandwidth costs based on data transfer
    """
    # AWS data transfer pricing (verify current rates)
    pricing = {
        'data_transfer_out_first_1gb': 0.00,  # First 1 GB free
        'data_transfer_out_next_9999gb': 0.09,  # Next 9.999 TB
        'data_transfer_out_next_40tb': 0.085,  # Next 40 TB
        'data_transfer_out_next_100tb': 0.07,  # Next 100 TB
        'data_transfer_out_over_150tb': 0.05   # Over 150 TB
    }
    
    gb_transferred = bytes_transferred / (1024**3)
    
    if gb_transferred <= 1:
        cost = 0
    elif gb_transferred <= 10000:
        cost = (gb_transferred - 1) * pricing['data_transfer_out_next_9999gb']
    else:
        # More complex calculation for higher tiers
        cost = 9999 * pricing['data_transfer_out_next_9999gb']
        remaining = gb_transferred - 10000
        
        if remaining <= 40000:
            cost += remaining * pricing['data_transfer_out_next_40tb']
        # ... additional tiers
    
    return {
        'gb_transferred': gb_transferred,
        'estimated_cost': cost,
        'rate_per_gb': cost / gb_transferred if gb_transferred > 0 else 0
    }

# Example usage
if __name__ == "__main__":
    # Analyze last 30 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    cost_data = analyze_s3_costs(start_date, end_date)
    generate_cost_report(cost_data)
    
    # Estimate bandwidth costs for recent upload
    bandwidth_estimate = estimate_bandwidth_costs(35469389)  # From our HLS upload
    print(f"\nBandwidth Cost Estimate:")
    print(f"Data Transferred: {bandwidth_estimate['gb_transferred']:.4f} GB")
    print(f"Estimated Cost: ${bandwidth_estimate['estimated_cost']:.6f}")
```

## 4. Automated Monitoring and Alerts

### CloudWatch Alarms for Cost Control
```python
def create_cost_alarms():
    """
    Create CloudWatch alarms for cost monitoring
    """
    cloudwatch = boto3.client('cloudwatch')
    
    # Alarm for high S3 costs
    cloudwatch.put_metric_alarm(
        AlarmName='S3-High-Daily-Cost',
        ComparisonOperator='GreaterThanThreshold',
        EvaluationPeriods=1,
        MetricName='EstimatedCharges',
        Namespace='AWS/Billing',
        Period=86400,
        Statistic='Maximum',
        Threshold=10.0,  # $10 daily threshold
        ActionsEnabled=True,
        AlarmActions=[
            'arn:aws:sns:us-west-1:123456789012:cost-alerts'
        ],
        AlarmDescription='Alert when S3 daily costs exceed $10',
        Dimensions=[
            {'Name': 'ServiceName', 'Value': 'AmazonS3'},
            {'Name': 'Currency', 'Value': 'USD'}
        ]
    )
    
    # Alarm for high bandwidth usage
    cloudwatch.put_metric_alarm(
        AlarmName='S3-High-Bandwidth-Usage',
        ComparisonOperator='GreaterThanThreshold',
        EvaluationPeriods=2,
        MetricName='BytesDownloaded',
        Namespace='AWS/S3',
        Period=3600,
        Statistic='Sum',
        Threshold=1073741824,  # 1 GB per hour
        ActionsEnabled=True,
        AlarmActions=[
            'arn:aws:sns:us-west-1:123456789012:bandwidth-alerts'
        ],
        AlarmDescription='Alert when S3 bandwidth exceeds 1 GB/hour',
        Dimensions=[
            {'Name': 'BucketName', 'Value': 'zmt-compressed-video'}
        ]
    )

# Create alarms
create_cost_alarms()
```
