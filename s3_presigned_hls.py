#!/usr/bin/env python3
"""
S3 Presigned URL HLS Streaming Module
Generates secure presigned URLs for HLS playlists and segments
"""

import boto3
import re
import os
import tempfile
from datetime import datetime, timedelta
from urllib.parse import urlparse
import json

class S3PresignedHLSManager:
    """
    Manages presigned URLs for HLS streaming from S3
    """
    
    def __init__(self, region='us-west-1', expiration_hours=24):
        """
        Initialize the presigned URL manager
        
        Args:
            region: AWS region for S3 client
            expiration_hours: How long presigned URLs should be valid (default 24 hours)
        """
        self.region = region
        self.expiration_seconds = expiration_hours * 3600
        self.s3_client = boto3.client('s3', region_name=region)
        
    def generate_presigned_url(self, bucket_name, object_key):
        """
        Generate a presigned URL for an S3 object
        
        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key
            
        Returns:
            Presigned URL string
        """
        try:
            presigned_url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket_name, 'Key': object_key},
                ExpiresIn=self.expiration_seconds
            )
            return presigned_url
        except Exception as e:
            print(f"❌ Failed to generate presigned URL for s3://{bucket_name}/{object_key}: {e}")
            return None
    
    def parse_s3_uri(self, s3_uri):
        """
        Parse S3 URI into bucket and key components
        
        Args:
            s3_uri: S3 URI like s3://bucket/path/to/object
            
        Returns:
            Tuple of (bucket_name, object_key)
        """
        parsed = urlparse(s3_uri)
        bucket_name = parsed.netloc
        object_key = parsed.path.lstrip('/')
        return bucket_name, object_key
    
    def download_playlist_from_s3(self, s3_playlist_uri):
        """
        Download playlist content from S3
        
        Args:
            s3_playlist_uri: S3 URI of the playlist file
            
        Returns:
            Playlist content as string, or None if failed
        """
        try:
            bucket_name, object_key = self.parse_s3_uri(s3_playlist_uri)
            
            response = self.s3_client.get_object(Bucket=bucket_name, Key=object_key)
            playlist_content = response['Body'].read().decode('utf-8')
            
            print(f"✅ Downloaded playlist from s3://{bucket_name}/{object_key}")
            return playlist_content
            
        except Exception as e:
            print(f"❌ Failed to download playlist from {s3_playlist_uri}: {e}")
            return None
    
    def create_presigned_playlist(self, s3_playlist_uri, output_path=None):
        """
        Create a new playlist file with presigned URLs for all segments
        
        Args:
            s3_playlist_uri: S3 URI of the original playlist
            output_path: Local path to save the presigned playlist (optional)
            
        Returns:
            Tuple of (presigned_playlist_content, presigned_playlist_url, segment_info)
        """
        # Download original playlist
        original_content = self.download_playlist_from_s3(s3_playlist_uri)
        if not original_content:
            return None, None, None
        
        # Parse S3 URI components
        bucket_name, playlist_key = self.parse_s3_uri(s3_playlist_uri)
        playlist_dir = os.path.dirname(playlist_key)
        
        # Track segment information
        segment_info = {
            'total_segments': 0,
            'segments': [],
            'playlist_duration': 0
        }
        
        # Process playlist line by line
        presigned_lines = []
        current_duration = 0
        
        for line in original_content.split('\n'):
            line = line.strip()
            
            # Check if line is a segment file reference
            if line.endswith('.ts'):
                # Construct full S3 key for segment
                if playlist_dir:
                    segment_key = f"{playlist_dir}/{line}"
                else:
                    segment_key = line
                
                # Generate presigned URL for segment
                segment_presigned_url = self.generate_presigned_url(bucket_name, segment_key)
                
                if segment_presigned_url:
                    presigned_lines.append(segment_presigned_url)
                    
                    # Track segment info
                    segment_info['total_segments'] += 1
                    segment_info['segments'].append({
                        'filename': line,
                        's3_key': segment_key,
                        'presigned_url': segment_presigned_url,
                        'duration': current_duration
                    })
                    
                    print(f"🔗 Generated presigned URL for segment: {line}")
                else:
                    # Fallback to original if presigned URL generation fails
                    presigned_lines.append(line)
                    print(f"⚠️  Using original URL for segment: {line}")
                
                current_duration = 0  # Reset for next segment
                
            elif line.startswith('#EXTINF:'):
                # Extract duration from EXTINF line
                duration_match = re.search(r'#EXTINF:([\d.]+)', line)
                if duration_match:
                    current_duration = float(duration_match.group(1))
                    segment_info['playlist_duration'] += current_duration
                
                presigned_lines.append(line)
            else:
                # Keep all other lines as-is (headers, comments, etc.)
                presigned_lines.append(line)
        
        # Create presigned playlist content
        presigned_content = '\n'.join(presigned_lines)
        
        # Generate presigned URL for the playlist itself
        playlist_presigned_url = self.generate_presigned_url(bucket_name, playlist_key)
        
        # Save to local file if requested
        if output_path:
            try:
                with open(output_path, 'w') as f:
                    f.write(presigned_content)
                print(f"💾 Saved presigned playlist to: {output_path}")
            except Exception as e:
                print(f"⚠️  Failed to save presigned playlist: {e}")
        
        print(f"🎯 Created presigned playlist with {segment_info['total_segments']} segments")
        print(f"📊 Total duration: {segment_info['playlist_duration']:.1f} seconds")
        
        return presigned_content, playlist_presigned_url, segment_info
    
    def create_streaming_manifest(self, s3_playlist_uri, output_dir=None):
        """
        Create a complete streaming manifest with presigned URLs and metadata
        
        Args:
            s3_playlist_uri: S3 URI of the original playlist
            output_dir: Directory to save manifest files (optional)
            
        Returns:
            Dictionary containing all streaming information
        """
        # Create presigned playlist
        presigned_content, playlist_url, segment_info = self.create_presigned_playlist(s3_playlist_uri)
        
        if not presigned_content:
            return None
        
        # Create manifest
        manifest = {
            'created_at': datetime.now().isoformat(),
            'expires_at': (datetime.now() + timedelta(seconds=self.expiration_seconds)).isoformat(),
            'original_playlist_s3_uri': s3_playlist_uri,
            'presigned_playlist_url': playlist_url,
            'presigned_playlist_content': presigned_content,
            'segment_info': segment_info,
            'streaming_info': {
                'total_segments': segment_info['total_segments'],
                'total_duration_seconds': segment_info['playlist_duration'],
                'average_segment_duration': segment_info['playlist_duration'] / segment_info['total_segments'] if segment_info['total_segments'] > 0 else 0,
                'estimated_bitrate': 'unknown'  # Could be calculated from segment sizes
            }
        }
        
        # Save files if output directory specified
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            
            # Save presigned playlist
            playlist_path = os.path.join(output_dir, 'presigned_playlist.m3u8')
            with open(playlist_path, 'w') as f:
                f.write(presigned_content)
            
            # Save manifest JSON
            manifest_path = os.path.join(output_dir, 'streaming_manifest.json')
            with open(manifest_path, 'w') as f:
                json.dump(manifest, f, indent=2)
            
            print(f"📁 Saved streaming files to: {output_dir}")
            print(f"   • Presigned playlist: {playlist_path}")
            print(f"   • Streaming manifest: {manifest_path}")
        
        return manifest
    
    def get_streaming_urls(self, s3_playlist_uri):
        """
        Get streaming URLs for immediate use
        
        Args:
            s3_playlist_uri: S3 URI of the original playlist
            
        Returns:
            Dictionary with streaming URLs and info
        """
        manifest = self.create_streaming_manifest(s3_playlist_uri)
        
        if not manifest:
            return None
        
        return {
            'playlist_url': manifest['presigned_playlist_url'],
            'direct_playlist_content': manifest['presigned_playlist_content'],
            'total_segments': manifest['segment_info']['total_segments'],
            'duration_seconds': manifest['segment_info']['playlist_duration'],
            'expires_at': manifest['expires_at']
        }

def create_presigned_hls_viewer(s3_playlist_uri, output_dir=None, expiration_hours=24):
    """
    Convenience function to create presigned HLS streaming setup
    
    Args:
        s3_playlist_uri: S3 URI of the HLS playlist
        output_dir: Directory to save presigned files (optional)
        expiration_hours: URL expiration time in hours
        
    Returns:
        Streaming manifest dictionary
    """
    manager = S3PresignedHLSManager(expiration_hours=expiration_hours)
    return manager.create_streaming_manifest(s3_playlist_uri, output_dir)

# Example usage
if __name__ == "__main__":
    # Example S3 playlist URI
    s3_playlist_uri = "s3://zmt-compressed-video/hls/enhanced_logging_big_test/playlist.m3u8"
    
    print("🎯 Creating presigned HLS streaming setup...")
    print(f"📍 Source: {s3_playlist_uri}")
    
    # Create presigned streaming manifest
    manifest = create_presigned_hls_viewer(
        s3_playlist_uri=s3_playlist_uri,
        output_dir="./presigned_hls",
        expiration_hours=24
    )
    
    if manifest:
        print("\n🎉 Presigned HLS streaming setup complete!")
        print(f"🔗 Playlist URL: {manifest['presigned_playlist_url']}")
        print(f"📊 Segments: {manifest['segment_info']['total_segments']}")
        print(f"⏱️  Duration: {manifest['segment_info']['playlist_duration']:.1f}s")
        print(f"⏰ Expires: {manifest['expires_at']}")
    else:
        print("❌ Failed to create presigned HLS streaming setup")
