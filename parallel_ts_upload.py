#!/usr/bin/env python3

"""
Parallel .ts File Upload Script
Uploads multiple .ts files from a local directory to S3 using parallel threads
Usage: python3 parallel_ts_upload.py <local_directory> <s3_bucket> <s3_prefix> [threads] [region]
"""

import os
import sys
import time
import argparse
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from datetime import datetime

class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

class UploadStats:
    def __init__(self):
        self.lock = threading.Lock()
        self.successful_uploads = 0
        self.failed_uploads = 0
        self.total_bytes = 0
        self.total_duration = 0
        self.failed_files = []
        self.upload_details = []

    def add_success(self, filename, file_size, duration, speed):
        with self.lock:
            self.successful_uploads += 1
            self.total_bytes += file_size
            self.total_duration += duration
            self.upload_details.append({
                'filename': filename,
                'size': file_size,
                'duration': duration,
                'speed': speed
            })

    def add_failure(self, filename, error):
        with self.lock:
            self.failed_uploads += 1
            self.failed_files.append({'filename': filename, 'error': str(error)})

def print_status(message):
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"{Colors.BLUE}[{timestamp}]{Colors.NC} {message}")

def print_success(message):
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"{Colors.GREEN}[{timestamp}]{Colors.NC} ✅ {message}")

def print_warning(message):
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"{Colors.YELLOW}[{timestamp}]{Colors.NC} ⚠️  {message}")

def print_error(message):
    timestamp = datetime.now().strftime('%H:%M:%S')
    print(f"{Colors.RED}[{timestamp}]{Colors.NC} ❌ {message}")

def format_size(size_bytes):
    """Format file size in human-readable format"""
    if size_bytes >= 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"
    elif size_bytes >= 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.2f} MB"
    elif size_bytes >= 1024:
        return f"{size_bytes / 1024:.2f} KB"
    else:
        return f"{size_bytes} bytes"

def upload_file(file_path, s3_client, bucket_name, s3_prefix, thread_id, stats):
    """Upload a single file to S3"""
    try:
        filename = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        s3_key = f"{s3_prefix}{filename}"
        
        size_display = format_size(file_size)
        print_status(f"[Thread {thread_id}] Uploading: {filename} ({size_display})")
        
        # Record start time
        start_time = time.time()
        
        # Upload file to S3
        s3_client.upload_file(file_path, bucket_name, s3_key)
        
        # Calculate upload time and speed
        end_time = time.time()
        duration = end_time - start_time
        speed_mbps = (file_size / (1024 * 1024)) / max(duration, 0.001)
        
        print_success(f"[Thread {thread_id}] ⏱️ Uploaded: {filename} | Size: {size_display} | Speed: {speed_mbps:.2f} MB/s | Duration: {duration:.2f}s")
        
        stats.add_success(filename, file_size, duration, speed_mbps)
        return True
        
    except Exception as e:
        print_error(f"[Thread {thread_id}] Failed to upload {filename}: {e}")
        stats.add_failure(filename, e)
        return False

def find_ts_files(directory):
    """Find all .ts files in the directory"""
    ts_files = []
    for file_path in Path(directory).rglob("*.ts"):
        if file_path.is_file():
            ts_files.append(str(file_path))
    return sorted(ts_files)

def validate_aws_credentials(region=None):
    """Validate AWS credentials"""
    try:
        session = boto3.Session()
        if region:
            sts = session.client('sts', region_name=region)
        else:
            sts = session.client('sts')
        sts.get_caller_identity()
        return True
    except (NoCredentialsError, ClientError) as e:
        print_error(f"AWS credentials not configured or invalid: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(
        description="Upload multiple .ts files from a local directory to S3 using parallel threads",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 parallel_ts_upload.py ./hls_output my-bucket output/stream/ --threads 4 --region us-east-1
  python3 parallel_ts_upload.py /tmp/segments video-bucket streams/live/ --threads 6
  python3 parallel_ts_upload.py ./segments bucket path/to/stream/

Environment Variables:
  AWS_REGION      - Default AWS region if not specified
  AWS_PROFILE     - AWS profile to use
        """
    )
    
    parser.add_argument('local_directory', help='Local directory containing .ts files')
    parser.add_argument('s3_bucket', help='S3 bucket name (without s3:// prefix)')
    parser.add_argument('s3_prefix', help='S3 key prefix (path within bucket)')
    parser.add_argument('--threads', '-t', type=int, default=6, 
                       help='Number of parallel threads (default: 6, range: 1-8)')
    parser.add_argument('--region', '-r', 
                       help='AWS region (default: from AWS_REGION env var)')
    
    args = parser.parse_args()
    
    # Validate inputs
    if not os.path.isdir(args.local_directory):
        print_error(f"Local directory does not exist: {args.local_directory}")
        sys.exit(1)
    
    if args.threads < 1 or args.threads > 8:
        print_error("Thread count must be between 1 and 8")
        sys.exit(1)
    
    # Set region
    region = args.region or os.environ.get('AWS_REGION')
    
    # Ensure S3 prefix ends with /
    s3_prefix = args.s3_prefix
    if s3_prefix and not s3_prefix.endswith('/'):
        s3_prefix += '/'
    
    # Validate AWS credentials
    if not validate_aws_credentials(region):
        sys.exit(1)
    
    # Find all .ts files
    print_status(f"Scanning for .ts files in: {args.local_directory}")
    ts_files = find_ts_files(args.local_directory)
    
    if not ts_files:
        print_warning(f"No .ts files found in directory: {args.local_directory}")
        sys.exit(0)
    
    print_status(f"Found {len(ts_files)} .ts files to upload")
    print_status(f"Using {args.threads} parallel threads")
    print_status(f"Target: s3://{args.s3_bucket}/{s3_prefix}")
    if region:
        print_status(f"Region: {region}")
    
    # Create S3 client
    if region:
        s3_client = boto3.client('s3', region_name=region)
    else:
        s3_client = boto3.client('s3')
    
    # Initialize statistics
    stats = UploadStats()
    
    # Start upload process
    print_status("🚀 Starting parallel upload...")
    start_time = time.time()
    
    # Upload files using ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=args.threads) as executor:
        # Submit all upload tasks
        future_to_file = {
            executor.submit(upload_file, file_path, s3_client, args.s3_bucket, s3_prefix, i % args.threads + 1, stats): file_path
            for i, file_path in enumerate(ts_files)
        }
        
        # Wait for all uploads to complete
        for future in as_completed(future_to_file):
            file_path = future_to_file[future]
            try:
                future.result()
            except Exception as e:
                filename = os.path.basename(file_path)
                print_error(f"Unexpected error uploading {filename}: {e}")
                stats.add_failure(filename, e)
    
    # Calculate total time
    end_time = time.time()
    total_wall_time = end_time - start_time
    
    # Calculate average speed
    if total_wall_time > 0:
        avg_speed = (stats.total_bytes / (1024 * 1024)) / total_wall_time
    else:
        avg_speed = 0.0
    
    # Print summary
    print()
    print("=" * 80)
    print_status("📊 UPLOAD SUMMARY")
    print("=" * 80)
    print_success(f"✅ Successful uploads: {stats.successful_uploads}")
    if stats.failed_uploads > 0:
        print_error(f"❌ Failed uploads: {stats.failed_uploads}")
    print_status(f"📦 Total data uploaded: {format_size(stats.total_bytes)}")
    print_status(f"⏱️  Total time: {total_wall_time:.2f}s")
    print_status(f"🚀 Average speed: {avg_speed:.2f} MB/s")
    print_status(f"🧵 Threads used: {args.threads}")
    print_status(f"📍 Destination: s3://{args.s3_bucket}/{s3_prefix}")
    
    # Show failed files if any
    if stats.failed_uploads > 0:
        print()
        print_warning("Failed uploads:")
        for failed in stats.failed_files:
            print(f"  - {failed['filename']}: {failed['error']}")
    
    print("=" * 80)
    
    if stats.failed_uploads == 0:
        print_success("🎉 All uploads completed successfully!")
        sys.exit(0)
    else:
        print_error("Some uploads failed. Check the summary above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
