<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Y4M Video Streamer</title>
    <link rel="stylesheet" href="style.css">
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🎬 Y4M Video Streamer</h1>
            <p>Upload, compress, and stream Y4M videos</p>
        </header>

        <div class="main-content">
            <!-- Video Players Section - Main Focus -->
            <section class="players-section">
                <!-- Original Video -->
                <div class="video-container">
                    <h2>🎥 Original Video</h2>
                    <div id="originalVideoInfo" class="video-info"></div>
                    <video id="originalVideo" controls preload="metadata" style="display: none;">
                        Your browser does not support the video tag.
                    </video>
                    <div id="originalVideoPlaceholder" class="video-placeholder">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">🎬</div>
                            <p>Select input source below to load video</p>
                        </div>
                    </div>
                </div>

                <!-- Compressed Video -->
                <div class="video-container">
                    <h2>⚡ Compressed Video</h2>
                    <div id="compressedVideoInfo" class="video-info"></div>
                    <video id="compressedVideo" controls preload="metadata" style="display: none;">
                        Your browser does not support the video tag.
                    </video>
                    <div id="compressedVideoPlaceholder" class="video-placeholder">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">⚡</div>
                            <p>Run compression to see results</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Main Compression Control -->
            <section class="compression-section">
                <h2>⚙️ Video Compression Control</h2>

                <!-- Input Source Selection -->
                <div class="input-source-section">
                    <h3>📁 Select Input Source</h3>

                    <!-- Single File Input -->
                    <div class="input-method">
                        <h4>Single File</h4>
                        <div class="file-input-controls">
                            <input type="file" id="fileInput" accept=".y4m,.mp4,.avi,.mov,.mkv" style="display: none;">
                            <button id="selectFileBtn" class="secondary-btn">Select File</button>
                            <span id="selectedFileName" class="selected-file">No file selected</span>
                        </div>
                    </div>

                    <!-- S3 Single File Input -->
                    <div class="input-method">
                        <h4>S3 Single File</h4>
                        <div class="s3-input">
                            <input type="text" id="s3Uri" placeholder="s3://bucket-name/path/to/video.y4m">
                            <button id="loadS3Btn" class="secondary-btn">Load from S3</button>
                        </div>
                    </div>

                    <!-- Batch Processing -->
                    <div class="input-method">
                        <h4>Batch Processing</h4>
                        <div class="batch-inputs">
                            <div class="input-group">
                                <label for="batchInputPath">Input Source:</label>
                                <div class="path-input-container">
                                    <input type="text" id="batchInputPath" placeholder="/path/to/input or s3://bucket/input/">
                                    <button type="button" id="selectInputDirBtn" class="dir-select-btn" title="Select Input Directory">📁</button>
                                </div>
                                <small>Local folder path or S3 URI containing .y4m files</small>
                            </div>

                            <div class="input-group">
                                <label for="batchOutputPath">Output Destination:</label>
                                <div class="path-input-container">
                                    <input type="text" id="batchOutputPath" placeholder="/path/to/output or s3://bucket/output/">
                                    <button type="button" id="selectOutputDirBtn" class="dir-select-btn" title="Select Output Directory">📁</button>
                                </div>
                                <small>Local folder path or S3 URI for compressed files</small>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden directory input elements -->
                    <input type="file" id="inputDirSelector" webkitdirectory directory multiple style="display: none;">
                    <input type="file" id="outputDirSelector" webkitdirectory directory multiple style="display: none;">
                </div>

                <!-- Compression Controls -->
                <div class="compression-controls-section">
                    <h3>🚀 Run Compression</h3>
                    <div class="compression-controls">
                        <button id="runSingleCompressionBtn" disabled class="primary-btn">Compress Single File</button>
                        <button id="runBatchCompressionBtn" class="primary-btn">Run Batch Compression</button>
                        <button id="cancelCompressionBtn" disabled class="cancel-btn">Cancel</button>
                    </div>
                </div>

                <!-- Status and Progress -->
                <div id="compressionStatus" class="compression-status"></div>
                <div id="compressionProgress" class="progress-container" style="display: none;">
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill"></div>
                    </div>
                    <span id="progressText">0%</span>
                </div>

                <!-- Compression Logs -->
                <div id="compressionLogs" class="logs-container" style="display: none;">
                    <h3>📋 Compression Logs</h3>
                    <div id="logsContent" class="logs-content"></div>
                </div>
            </section>


        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
