<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Y4M Video Streamer</title>
    <link rel="stylesheet" href="style.css">
    <!-- HLS.js for HLS video streaming support -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🎬 Y4M Video Streamer</h1>
            <p>Upload, compress, and stream Y4M videos</p>
        </header>

        <div class="main-content">
            <!-- Video Players Section - Main Focus -->
            <section class="players-section">
                <!-- Original Video -->
                <div class="video-container">
                    <h2>🎥 Original Video</h2>
                    <div class="video-selection">
                        <button id="selectOriginalVideoBtn" class="video-select-btn">📁 Select Video</button>
                        <select id="originalVideoSelect" class="video-dropdown" style="display: none;">
                            <option value="">Choose a video...</option>
                        </select>
                    </div>
                    <div id="originalVideoInfo" class="video-info"></div>
                    <video id="originalVideo" controls preload="metadata" style="display: none;">
                        Your browser does not support the video tag.
                    </video>
                    <div id="originalVideoPlaceholder" class="video-placeholder">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">🎬</div>
                            <p>Click "Select Video" to choose a video</p>
                        </div>
                    </div>
                </div>

                <!-- Compressed Video -->
                <div class="video-container">
                    <h2>⚡ Compressed Video</h2>
                    <div class="video-selection">
                        <button id="selectCompressedVideoBtn" class="video-select-btn">📁 Select Video</button>
                        <button id="testHLSBtn" class="video-select-btn" style="background: #e74c3c; margin-left: 10px;">🧪 Test HLS</button>
                        <select id="compressedVideoSelect" class="video-dropdown" style="display: none;">
                            <option value="">Choose a video...</option>
                        </select>
                    </div>
                    <div id="compressedVideoInfo" class="video-info"></div>
                    <video id="compressedVideo" controls preload="metadata" style="display: none;">
                        Your browser does not support the video tag.
                    </video>
                    <div id="compressedVideoPlaceholder" class="video-placeholder">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">⚡</div>
                            <p>Click "Select Video" to choose a video</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Main Compression Control -->
            <section class="compression-section">
                <h2>⚙️ Video Compression Control</h2>

                <!-- Input Source Selection -->
                <div class="input-source-section">
                    <h3>📁 Select Input Source</h3>

                    <!-- Batch Processing -->
                    <div class="input-method">
                        <h4>Batch Processing</h4>
                        <div class="batch-inputs">
                            <div class="input-group">
                                <label for="batchInputPath">Input Source:</label>
                                <div class="path-input-container">
                                    <input type="text" id="batchInputPath" placeholder="/path/to/input or s3://zmt-source-video/input/">
                                    <button type="button" id="selectInputDirBtn" class="dir-select-btn" title="Select Input Directory">📁</button>
                                </div>
                                <small>Local folder path or S3 URI (s3://bucket-name/path/) containing .y4m files</small>
                            </div>
                            
                            <div class="input-group s3-region-group" id="inputRegionGroup" style="display: none;">
                                <label for="inputRegion">Source Region:</label>
                                <select id="inputRegion" class="region-select">
                                    <option value="us-east-1">US East (N. Virginia)</option>
                                    <option value="us-east-2">US East (Ohio)</option>
                                    <option value="us-west-1">US West (N. California)</option>
                                    <option value="us-west-2">US West (Oregon)</option>
                                    <option value="eu-west-1">EU (Ireland)</option>
                                    <option value="eu-central-1">EU (Frankfurt)</option>
                                    <option value="ap-northeast-1">Asia Pacific (Tokyo)</option>
                                    <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
                                    <option value="ap-southeast-2">Asia Pacific (Sydney)</option>
                                </select>
                            </div>

                            <div class="input-group">
                                <label for="batchOutputPath">Output Destination:</label>
                                <div class="path-input-container">
                                    <input type="text" id="batchOutputPath" placeholder="/path/to/output or s3://zmt-compressed-video/output/">
                                    <button type="button" id="selectOutputDirBtn" class="dir-select-btn" title="Select Output Directory">📁</button>
                                </div>
                                <small>Local folder path or S3 URI (s3://bucket-name/path/) for compressed files</small>
                            </div>
                            
                            <div class="input-group s3-region-group" id="outputRegionGroup" style="display: none;">
                                <label for="outputRegion">Destination Region:</label>
                                <select id="outputRegion" class="region-select">
                                    <option value="us-east-1">US East (N. Virginia)</option>
                                    <option value="us-east-2">US East (Ohio)</option>
                                    <option value="us-west-1">US West (N. California)</option>
                                    <option value="us-west-2">US West (Oregon)</option>
                                    <option value="eu-west-1">EU (Ireland)</option>
                                    <option value="eu-central-1">EU (Frankfurt)</option>
                                    <option value="ap-northeast-1">Asia Pacific (Tokyo)</option>
                                    <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
                                    <option value="ap-southeast-2">Asia Pacific (Sydney)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden directory input elements -->
                    <input type="file" id="inputDirSelector" webkitdirectory directory multiple style="display: none;">
                    <input type="file" id="outputDirSelector" webkitdirectory directory multiple style="display: none;">
                </div>

                <!-- Compression Controls -->
                <div class="compression-controls-section">
                    <h3>🚀 Run Compression</h3>
                    <div class="compression-controls">
                        <button id="runBatchCompressionBtn" class="primary-btn">Run Batch Compression</button>
                        <button id="cancelCompressionBtn" disabled class="cancel-btn">Cancel</button>
                    </div>
                </div>

                <!-- Status and Progress -->
                <div id="compressionStatus" class="compression-status"></div>
                <div id="compressionProgress" class="progress-container" style="display: none;">
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill"></div>
                    </div>
                    <span id="progressText">0%</span>
                </div>

                <!-- Compression Logs -->
                <div id="compressionLogs" class="logs-container" style="display: none;">
                    <h3>📋 Compression Logs</h3>
                    <div id="logsContent" class="logs-content"></div>
                </div>
            </section>


        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
