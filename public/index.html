<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Y4M Video Streamer</title>
    <link rel="stylesheet" href="style.css">
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🎬 Y4M Video Streamer</h1>
            <p>Upload, compress, and stream Y4M videos</p>
        </header>

        <div class="main-content">
            <!-- Input Section -->
            <section class="input-section">
                <h2>📁 Video Input</h2>
                
                <!-- File Upload -->
                <div class="input-method">
                    <h3>Local File Upload</h3>
                    <div class="upload-area" id="uploadArea">
                        <input type="file" id="fileInput" accept=".y4m,.mp4,.avi,.mov,.mkv" hidden>
                        <div class="upload-content">
                            <div class="upload-icon">📁</div>
                            <p>Click to select or drag & drop a video file</p>
                            <small>Supported: Y4M, MP4, AVI, MOV, MKV</small>
                        </div>
                    </div>
                </div>

                <!-- S3 Input -->
                <div class="input-method">
                    <h3>Amazon S3 Input</h3>
                    <div class="s3-input">
                        <input type="text" id="s3Uri" placeholder="s3://bucket-name/path/to/video.y4m">
                        <button id="loadS3Btn">Load from S3</button>
                    </div>
                </div>

                <!-- Local Files List -->
                <div class="input-method">
                    <h3>Local Files</h3>
                    <button id="refreshLocalBtn">Refresh List</button>
                    <div id="localFilesList" class="files-list"></div>
                </div>
            </section>

            <!-- Video Players Section -->
            <section class="players-section">
                <!-- Original Video -->
                <div class="video-container">
                    <h2>🎥 Original Video</h2>
                    <div id="originalVideoInfo" class="video-info"></div>
                    <video id="originalVideo" controls preload="metadata" style="display: none;">
                        Your browser does not support the video tag.
                    </video>
                    <div id="originalVideoPlaceholder" class="video-placeholder">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">🎬</div>
                            <p>No video loaded</p>
                        </div>
                    </div>
                </div>

                <!-- Compressed Video -->
                <div class="video-container">
                    <h2>⚡ Compressed Video</h2>
                    <div id="compressedVideoInfo" class="video-info"></div>
                    <video id="compressedVideo" controls preload="metadata" style="display: none;">
                        Your browser does not support the video tag.
                    </video>
                    <div id="compressedVideoPlaceholder" class="video-placeholder">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">⚡</div>
                            <p>No compressed video available</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Compression Control -->
            <section class="compression-section">
                <h2>⚙️ Compression Control</h2>

                <!-- Compression Mode Selection -->
                <div class="compression-mode">
                    <h3>Compression Mode</h3>
                    <div class="mode-selection">
                        <label>
                            <input type="radio" name="compressionMode" value="single" checked>
                            Single File (Current Video)
                        </label>
                        <label>
                            <input type="radio" name="compressionMode" value="batch">
                            Batch Processing (Folder/S3)
                        </label>
                    </div>
                </div>

                <!-- Single File Mode -->
                <div id="singleFileMode" class="compression-mode-section">
                    <div class="compression-controls">
                        <button id="runCompressionBtn" disabled>Run Compression</button>
                        <button id="cancelCompressionBtn" disabled>Cancel</button>
                    </div>
                </div>

                <!-- Batch Mode -->
                <div id="batchMode" class="compression-mode-section" style="display: none;">
                    <div class="batch-inputs">
                        <div class="input-group">
                            <label for="batchInputPath">Input Source:</label>
                            <input type="text" id="batchInputPath" placeholder="/path/to/input or s3://bucket/input/">
                            <small>Local folder path or S3 URI containing .y4m files</small>
                        </div>

                        <div class="input-group">
                            <label for="batchOutputPath">Output Destination:</label>
                            <input type="text" id="batchOutputPath" placeholder="/path/to/output or s3://bucket/output/">
                            <small>Local folder path or S3 URI for compressed files</small>
                        </div>
                    </div>

                    <div class="compression-controls">
                        <button id="runBatchCompressionBtn">Run Batch Compression</button>
                        <button id="cancelBatchCompressionBtn" disabled>Cancel</button>
                    </div>
                </div>

                <div id="compressionStatus" class="compression-status"></div>
                <div id="compressionProgress" class="progress-container" style="display: none;">
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill"></div>
                    </div>
                    <span id="progressText">0%</span>
                </div>

                <div id="compressionLogs" class="logs-container" style="display: none;">
                    <h3>Compression Logs</h3>
                    <div id="logsContent" class="logs-content"></div>
                </div>
            </section>

            <!-- Compressed Videos List -->
            <section class="compressed-list-section">
                <h2>📚 Compressed Videos</h2>
                <button id="refreshCompressedBtn">Refresh List</button>
                <div id="compressedFilesList" class="files-list"></div>
            </section>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
