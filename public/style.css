* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    grid-template-rows: auto auto auto;
    gap: 20px;
    grid-template-areas: 
        "input players"
        "compression players"
        "compressed-list players";
}

.input-section {
    grid-area: input;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.players-section {
    grid-area: players;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.compression-section {
    grid-area: compression;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.compressed-list-section {
    grid-area: compressed-list;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.video-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    height: fit-content;
}

.video-container h2 {
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.video-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

video {
    width: 100%;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.video-placeholder {
    width: 100%;
    height: 200px;
    background: #f8f9fa;
    border: 2px dashed #ddd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.placeholder-content {
    text-align: center;
    color: #999;
}

.placeholder-icon {
    font-size: 3rem;
    margin-bottom: 10px;
}

.input-method {
    margin-bottom: 25px;
}

.input-method h3 {
    margin-bottom: 10px;
    color: #555;
}

.upload-area {
    border: 2px dashed #667eea;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

.upload-area:hover {
    border-color: #5a67d8;
    background: #f0f4ff;
}

.upload-area.dragover {
    border-color: #4c51bf;
    background: #e6fffa;
}

.upload-content {
    pointer-events: none;
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.s3-input {
    display: flex;
    gap: 10px;
}

.s3-input input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

button {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s ease;
}

button:hover:not(:disabled) {
    background: #5a67d8;
}

button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.compression-mode {
    margin-bottom: 20px;
}

.compression-mode h3 {
    margin-bottom: 10px;
    color: #555;
}

.mode-selection {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.mode-selection label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
}

.mode-selection input[type="radio"] {
    margin: 0;
}

.compression-mode-section {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    background: #fafafa;
}

.batch-inputs {
    margin-bottom: 15px;
}

.input-group {
    margin-bottom: 15px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.input-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.input-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

.compression-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.compression-status {
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-weight: 500;
}

.status-running {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-completed {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.progress-container {
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.logs-container {
    border: 1px solid #ddd;
    border-radius: 6px;
    max-height: 200px;
    overflow-y: auto;
}

.logs-content {
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background: #f8f9fa;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
    border-bottom: 1px solid #eee;
}

.log-timestamp {
    color: #666;
    margin-right: 10px;
}

.files-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 6px;
    margin-top: 10px;
}

.file-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background 0.2s ease;
}

.file-item:hover {
    background: #f8f9fa;
}

.file-item:last-child {
    border-bottom: none;
}

.file-name {
    font-weight: 500;
    margin-bottom: 5px;
}

.file-details {
    font-size: 12px;
    color: #666;
}

@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-areas: 
            "input"
            "players"
            "compression"
            "compressed-list";
    }
    
    .players-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .compression-controls {
        flex-direction: column;
    }
    
    .s3-input {
        flex-direction: column;
    }
}
