* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1b1b1b;
    min-height: 100vh;
    color: #e0e0e0;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: #FC9546;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
    gap: 30px;
    max-width: 1600px;
    margin: 0 auto;
}

.players-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 20px;
}

.compression-section {
    background: #2a2a2a;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    border: 1px solid #3a3a3a;
}

.video-container {
    background: #2a2a2a;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    height: fit-content;
    min-height: 500px;
    border: 1px solid #3a3a3a;
}

.video-selection {
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.video-select-btn {
    background: #639884;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 120px;
}

.video-select-btn:hover {
    background: #527a6a;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 152, 132, 0.3);
}

.video-dropdown {
    flex: 1;
    padding: 10px;
    border: 1px solid #4a4a4a;
    border-radius: 6px;
    font-size: 14px;
    background: #3a3a3a;
    color: #e0e0e0;
    cursor: pointer;
}

.video-dropdown:focus {
    outline: none;
    border-color: #639884;
    box-shadow: 0 0 0 2px rgba(99, 152, 132, 0.2);
}

.video-container h2 {
    margin-bottom: 15px;
    color: #FC9546;
    border-bottom: 2px solid #FC9546;
    padding-bottom: 10px;
}

.video-info {
    background: #3a3a3a;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #b0b0b0;
    border: 1px solid #4a4a4a;
}

video {
    width: 100%;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    background: #000;
}

video::-webkit-media-controls-panel {
    background-color: rgba(0, 0, 0, 0.8);
}

video::-webkit-media-controls-play-button,
video::-webkit-media-controls-volume-slider,
video::-webkit-media-controls-mute-button,
video::-webkit-media-controls-timeline,
video::-webkit-media-controls-current-time-display,
video::-webkit-media-controls-time-remaining-display {
    filter: brightness(1.2);
}

.video-placeholder {
    width: 100%;
    height: 200px;
    background: #3a3a3a;
    border: 2px dashed #5a5a5a;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.placeholder-content {
    text-align: center;
    color: #888;
}

.placeholder-icon {
    font-size: 3rem;
    margin-bottom: 10px;
}

.input-source-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #3a3a3a;
    border-radius: 8px;
    border: 1px solid #4a4a4a;
}

.input-source-section h3 {
    margin-bottom: 20px;
    color: #FC9546;
    border-bottom: 2px solid #FC9546;
    padding-bottom: 10px;
}

.input-method {
    margin-bottom: 25px;
    padding: 15px;
    background: #2a2a2a;
    border-radius: 6px;
    border: 1px solid #4a4a4a;
}

.input-method h4 {
    margin-bottom: 10px;
    color: #e0e0e0;
    font-size: 16px;
}

.file-input-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.selected-file {
    color: #666;
    font-style: italic;
    font-size: 14px;
}

.compression-controls-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #3a3a3a;
    border-radius: 8px;
    border: 1px solid #4a4a4a;
}

.compression-controls-section h3 {
    margin-bottom: 15px;
    color: #FC9546;
}

.upload-area {
    border: 2px dashed #667eea;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9ff;
}

.upload-area:hover {
    border-color: #5a67d8;
    background: #f0f4ff;
}

.upload-area.dragover {
    border-color: #4c51bf;
    background: #e6fffa;
}

.upload-content {
    pointer-events: none;
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.s3-input {
    display: flex;
    gap: 10px;
}

.s3-input input {
    flex: 1;
    padding: 10px;
    border: 1px solid #4a4a4a;
    border-radius: 6px;
    font-size: 14px;
    background: #3a3a3a;
    color: #e0e0e0;
}

.s3-region-group {
    margin-top: -10px;
    margin-bottom: 15px;
    padding-left: 20px;
    border-left: 3px solid #4a4a4a;
}

.region-select {
    width: 100%;
    padding: 8px;
    border: 1px solid #4a4a4a;
    border-radius: 6px;
    font-size: 14px;
    background-color: #3a3a3a;
    color: #e0e0e0;
}

.s3-region-group label {
    font-size: 13px;
    color: #b0b0b0;
}

button {
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 120px;
}

.primary-btn {
    background: #FC9546;
    color: white;
}

.primary-btn:hover:not(:disabled) {
    background: #e8843a;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(252, 149, 70, 0.3);
}

.secondary-btn {
    background: #639884;
    color: white;
}

.secondary-btn:hover:not(:disabled) {
    background: #527a6a;
}

.cancel-btn {
    background: #dc3545;
    color: white;
}

.cancel-btn:hover:not(:disabled) {
    background: #c82333;
}

button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.compression-mode {
    margin-bottom: 20px;
}

.compression-mode h3 {
    margin-bottom: 10px;
    color: #e0e0e0;
}

.mode-selection {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.mode-selection label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #e0e0e0;
}

.mode-selection input[type="radio"] {
    margin: 0;
}

.compression-mode-section {
    border: 1px solid #4a4a4a;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    background: #3a3a3a;
}

.batch-inputs {
    margin-bottom: 15px;
}

.input-group {
    margin-bottom: 15px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #e0e0e0;
}

.input-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #4a4a4a;
    border-radius: 6px;
    font-size: 14px;
    background: #3a3a3a;
    color: #e0e0e0;
}

.path-input-container {
    display: flex;
    gap: 8px;
    align-items: center;
}

.path-input-container input {
    flex: 1;
    margin: 0;
}

.dir-select-btn {
    background: #639884;
    color: white;
    border: none;
    padding: 10px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    min-width: auto;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.dir-select-btn:hover {
    background: #527a6a;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(99, 152, 132, 0.3);
}

.dir-select-btn:active {
    transform: translateY(0);
}

/* Path validation styles */
.path-valid {
    border-color: #639884 !important;
    background-color: #2a3a32 !important;
}

.path-invalid {
    border-color: #dc3545 !important;
    background-color: #3a2a2a !important;
}

.path-s3 {
    border-color: #FC9546 !important;
    background-color: #3a322a !important;
}

/* Path validation icons */
.path-input-container {
    position: relative;
}

.path-input-container::after {
    content: '';
    position: absolute;
    right: 60px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.path-input-container:has(.path-valid)::after {
    content: '✓';
    background: #639884;
    color: white;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.path-input-container:has(.path-invalid)::after {
    content: '✗';
    background: #dc3545;
    color: white;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.path-input-container:has(.path-s3)::after {
    content: '☁';
    background: #FC9546;
    color: white;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.input-group small {
    display: block;
    margin-top: 5px;
    color: #b0b0b0;
    font-size: 12px;
}

.compression-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.compression-status {
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-weight: 500;
}

.status-running {
    background: #3a3220;
    color: #FC9546;
    border: 1px solid #4a4030;
}

.status-completed {
    background: #2a3a32;
    color: #639884;
    border: 1px solid #3a4a42;
}

.status-failed {
    background: #3a2a2a;
    color: #dc3545;
    border: 1px solid #4a3a3a;
}

.progress-container {
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #3a3a3a;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 5px;
    border: 1px solid #4a4a4a;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #FC9546, #639884);
    width: 0%;
    transition: width 0.3s ease;
}

.logs-container {
    border: 1px solid #4a4a4a;
    border-radius: 6px;
    max-height: 200px;
    overflow-y: auto;
}

.logs-content {
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background: #2a2a2a;
    color: #e0e0e0;
}

.log-entry {
    margin-bottom: 5px;
    padding: 2px 0;
    border-bottom: 1px solid #3a3a3a;
}

.log-timestamp {
    color: #b0b0b0;
    margin-right: 10px;
}

.files-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #4a4a4a;
    border-radius: 6px;
    margin-top: 10px;
    background: #2a2a2a;
}

.file-item {
    padding: 10px;
    border-bottom: 1px solid #3a3a3a;
    cursor: pointer;
    transition: background 0.2s ease;
    color: #e0e0e0;
}

.file-item:hover {
    background: #3a3a3a;
}

.file-item:last-child {
    border-bottom: none;
}

.file-name {
    font-weight: 500;
    margin-bottom: 5px;
    color: #FC9546;
}

.file-details {
    font-size: 12px;
    color: #b0b0b0;
}

@media (max-width: 1200px) {
    .players-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .video-container {
        min-height: 400px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    .compression-controls {
        flex-direction: column;
        gap: 10px;
    }

    .file-input-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .s3-input {
        flex-direction: column;
        gap: 10px;
    }

    .path-input-container {
        flex-direction: column;
        gap: 8px;
    }

    .path-input-container input {
        width: 100%;
    }

    .dir-select-btn {
        width: 100%;
        min-width: 120px;
    }

    .video-selection {
        flex-direction: column;
        gap: 8px;
    }

    .video-select-btn {
        width: 100%;
        min-width: 100px;
    }

    .video-dropdown {
        width: 100%;
    }

    .input-method {
        padding: 10px;
    }

    .compression-section {
        padding: 20px;
    }

    button {
        min-width: 100px;
        padding: 10px 16px;
    }
}
