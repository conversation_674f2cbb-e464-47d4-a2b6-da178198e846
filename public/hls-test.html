<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLS Test</title>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
</head>
<body>
    <h1>HLS Test Page</h1>
    
    <div>
        <h2>HLS.js Status</h2>
        <p id="hlsStatus">Checking...</p>
    </div>
    
    <div>
        <h2>Test Video</h2>
        <video id="testVideo" controls width="640" height="360">
            Your browser does not support the video tag.
        </video>
        <br>
        <button onclick="loadHLSStream()">Load HLS Stream</button>
        <button onclick="testDirectURL()">Test Direct URL</button>
    </div>
    
    <div>
        <h2>Debug Info</h2>
        <pre id="debugInfo"></pre>
    </div>

    <script>
        const video = document.getElementById('testVideo');
        const statusEl = document.getElementById('hlsStatus');
        const debugEl = document.getElementById('debugInfo');
        
        function log(message) {
            console.log(message);
            debugEl.textContent += new Date().toISOString() + ': ' + message + '\n';
        }
        
        // Check HLS.js availability
        if (typeof Hls !== 'undefined') {
            if (Hls.isSupported()) {
                statusEl.textContent = 'HLS.js is available and supported';
                statusEl.style.color = 'green';
            } else {
                statusEl.textContent = 'HLS.js is available but not supported';
                statusEl.style.color = 'orange';
            }
        } else {
            statusEl.textContent = 'HLS.js is not available';
            statusEl.style.color = 'red';
        }
        
        function loadHLSStream() {
            const hlsUrl = '/api/video/hls/zmt-compressed-video/auto_test_live_demo/playlist.m3u8';
            log('Loading HLS stream: ' + hlsUrl);
            
            if (Hls.isSupported()) {
                const hls = new Hls({
                    debug: true,
                    enableWorker: true
                });
                
                hls.loadSource(hlsUrl);
                hls.attachMedia(video);
                
                hls.on(Hls.Events.MANIFEST_PARSED, function() {
                    log('HLS manifest parsed successfully');
                    video.play().catch(e => log('Auto-play failed: ' + e.message));
                });
                
                hls.on(Hls.Events.ERROR, function(event, data) {
                    log('HLS error: ' + JSON.stringify(data, null, 2));
                });
                
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                log('Using native HLS support');
                video.src = hlsUrl;
                video.play().catch(e => log('Auto-play failed: ' + e.message));
            } else {
                log('HLS not supported');
            }
        }
        
        function testDirectURL() {
            const directUrl = '/api/video/hls/zmt-compressed-video/auto_test_live_demo/playlist.m3u8';
            log('Testing direct URL: ' + directUrl);
            
            fetch(directUrl)
                .then(response => {
                    log('Response status: ' + response.status);
                    log('Response headers: ' + JSON.stringify([...response.headers.entries()]));
                    return response.text();
                })
                .then(text => {
                    log('Response body: ' + text);
                })
                .catch(error => {
                    log('Fetch error: ' + error.message);
                });
        }
        
        log('HLS test page loaded');
    </script>
</body>
</html>
