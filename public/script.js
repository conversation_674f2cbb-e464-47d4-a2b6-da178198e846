// Initialize Socket.IO
const socket = io();

// Global variables
let currentVideo = null;
let currentCompressionJob = null;
let autoScrollEnabled = true;

// DOM elements
const originalVideo = document.getElementById('originalVideo');
const originalVideoInfo = document.getElementById('originalVideoInfo');
const originalVideoPlaceholder = document.getElementById('originalVideoPlaceholder');
const compressedVideo = document.getElementById('compressedVideo');
const compressedVideoInfo = document.getElementById('compressedVideoInfo');
const compressedVideoPlaceholder = document.getElementById('compressedVideoPlaceholder');
const runBatchCompressionBtn = document.getElementById('runBatchCompressionBtn');
const cancelCompressionBtn = document.getElementById('cancelCompressionBtn');
const batchInputPath = document.getElementById('batchInputPath');
const batchOutputPath = document.getElementById('batchOutputPath');
const selectInputDirBtn = document.getElementById('selectInputDirBtn');
const selectOutputDirBtn = document.getElementById('selectOutputDirBtn');
const inputDirSelector = document.getElementById('inputDirSelector');
const outputDirSelector = document.getElementById('outputDirSelector');
const selectOriginalVideoBtn = document.getElementById('selectOriginalVideoBtn');
const selectCompressedVideoBtn = document.getElementById('selectCompressedVideoBtn');
const testHLSBtn = document.getElementById('testHLSBtn');
const originalVideoSelect = document.getElementById('originalVideoSelect');
const compressedVideoSelect = document.getElementById('compressedVideoSelect');
const compressionStatus = document.getElementById('compressionStatus');
const compressionProgress = document.getElementById('compressionProgress');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const compressionLogs = document.getElementById('compressionLogs');
const logsContent = document.getElementById('logsContent');
const autoScrollToggle = document.getElementById('autoScrollToggle');
const clearLogsBtn = document.getElementById('clearLogsBtn');
const refreshCompressedBtn = document.getElementById('refreshCompressedBtn');
const compressedFilesList = document.getElementById('compressedFilesList');
const inputRegionGroup = document.getElementById('inputRegionGroup');
const outputRegionGroup = document.getElementById('outputRegionGroup');
const inputRegion = document.getElementById('inputRegion');
const outputRegion = document.getElementById('outputRegion');

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    addPathValidation();
    setupVideoSync();
});

// Event listeners
function setupEventListeners() {
    // Directory selection
    selectInputDirBtn.addEventListener('click', () => inputDirSelector.click());
    selectOutputDirBtn.addEventListener('click', () => outputDirSelector.click());
    inputDirSelector.addEventListener('change', handleInputDirSelect);
    outputDirSelector.addEventListener('change', handleOutputDirSelect);

    // Compression controls
    runBatchCompressionBtn.addEventListener('click', startBatchCompression);
    cancelCompressionBtn.addEventListener('click', cancelCompression);

    // Video selection controls
    selectOriginalVideoBtn.addEventListener('click', showOriginalVideoSelection);
    selectCompressedVideoBtn.addEventListener('click', showCompressedVideoSelection);
    testHLSBtn.addEventListener('click', testHLSPlayback);
    originalVideoSelect.addEventListener('change', loadSelectedOriginalVideo);
    compressedVideoSelect.addEventListener('change', loadSelectedCompressedVideo);

    // Setup S3 region controls
    setupS3RegionControls();

    // Setup log controls
    setupLogControls();
}

// Socket.IO event listeners
socket.on('compressionStatus', (status) => {
    updateCompressionStatus(status);
});

socket.on('compressionProgress', (data) => {
    updateProgress(data.progress);
});

socket.on('compressionComplete', (status) => {
    updateCompressionStatus(status);
    refreshCompressedVideoList(); // Refresh compressed videos when compression completes
});

socket.on('compressionError', (data) => {
    showError(`Compression failed: ${data.error}`);
});

socket.on('compressionLog', (data) => {
    // Handle real-time log updates
    if (currentCompressionJob === data.jobId) {
        appendLogEntry(data.message, data.type);
    }
});

socket.on('inputFilesDetected', (data) => {
    refreshOriginalVideoList(); // Refresh original videos when new inputs detected
});

socket.on('outputFileCreated', (data) => {
    refreshCompressedVideoList(); // Refresh compressed videos when new outputs created
});



// Directory selection handlers
function handleInputDirSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        const firstFile = files[0];
        const dirInfo = extractDirectoryInfo(firstFile);

        batchInputPath.value = dirInfo.path;
        showStatus(`Input directory selected: ${dirInfo.displayName} (${files.length} files)`, 'completed');

        // Add visual feedback
        selectInputDirBtn.style.background = '#218838';
        selectInputDirBtn.textContent = '✓';
        setTimeout(() => {
            selectInputDirBtn.style.background = '#28a745';
            selectInputDirBtn.textContent = '📁';
        }, 2000);
    }
}

function handleOutputDirSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        const firstFile = files[0];
        const dirInfo = extractDirectoryInfo(firstFile);

        batchOutputPath.value = dirInfo.path;
        showStatus(`Output directory selected: ${dirInfo.displayName}`, 'completed');

        // Add visual feedback
        selectOutputDirBtn.style.background = '#218838';
        selectOutputDirBtn.textContent = '✓';
        setTimeout(() => {
            selectOutputDirBtn.style.background = '#28a745';
            selectOutputDirBtn.textContent = '📁';
        }, 2000);
    }
}

// Helper function to extract directory information
function extractDirectoryInfo(file) {
    const fullPath = file.webkitRelativePath || file.name;
    const pathParts = fullPath.split('/');

    // Remove the filename to get just the directory
    pathParts.pop();

    // Get the directory name
    const dirName = pathParts[pathParts.length - 1] || 'selected_directory';

    // Create a relative path that the server can understand
    const relativePath = `./${dirName}`;

    return {
        path: relativePath,
        displayName: dirName,
        fullPath: pathParts.join('/'),
        depth: pathParts.length
    };
}

// Add real-time path validation
function addPathValidation() {
    // Add input event listeners for real-time validation
    batchInputPath.addEventListener('input', debounce(validateInputPath, 500));
    batchOutputPath.addEventListener('input', debounce(validateOutputPath, 500));
}

// Validate input path in real-time
async function validateInputPath() {
    const path = batchInputPath.value.trim();
    if (!path) return;

    const validation = await validatePath(path);
    updatePathValidationUI(batchInputPath, validation);
}

// Validate output path in real-time
async function validateOutputPath() {
    const path = batchOutputPath.value.trim();
    if (!path) return;

    const validation = await validatePath(path);
    updatePathValidationUI(batchOutputPath, validation);
}

// Update UI based on path validation
function updatePathValidationUI(inputElement, validation) {
    // Remove existing validation classes
    inputElement.classList.remove('path-valid', 'path-invalid', 'path-s3');

    if (validation.type === 's3') {
        inputElement.classList.add('path-s3');
    } else if (validation.valid) {
        inputElement.classList.add('path-valid');
    } else {
        inputElement.classList.add('path-invalid');
    }
}

// Debounce function for input validation
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}



// Video selection functions
async function refreshVideoLists() {
    try {
        // Refresh both lists separately
        await refreshOriginalVideoList();
        await refreshCompressedVideoList();
    } catch (error) {
        console.error('Failed to refresh video lists:', error);
    }
}

async function refreshOriginalVideoList() {
    try {
        // Get available videos from uploads directory
        const response = await fetch('/api/video/list/uploads');
        const result = await response.json();

        if (result.success) {
            updateOriginalVideoDropdown(result.videos);
        }
    } catch (error) {
        console.error('Failed to refresh original video list:', error);
    }
}

async function refreshCompressedVideoList() {
    try {
        // Get available videos from compressed directory
        const response = await fetch('/api/video/list/compressed');
        const result = await response.json();

        if (result.success) {
            updateCompressedVideoDropdown(result.videos);
        }
    } catch (error) {
        console.error('Failed to refresh compressed video list:', error);
    }
}

function updateOriginalVideoDropdown(videos) {
    // Clear existing options
    originalVideoSelect.innerHTML = '<option value="">Choose an original video...</option>';

    // Add upload videos to original dropdown
    videos.forEach(video => {
        const option = document.createElement('option');
        option.value = JSON.stringify(video);
        option.textContent = `${video.filename}${video.isY4M ? ' (Y4M)' : ''}`;
        originalVideoSelect.appendChild(option);
    });
}

function updateCompressedVideoDropdown(videos) {
    // Clear existing options
    compressedVideoSelect.innerHTML = '<option value="">Choose a compressed video...</option>';

    // Add compressed videos to compressed dropdown
    videos.forEach(video => {
        const option = document.createElement('option');
        option.value = JSON.stringify(video);
        option.textContent = `${video.filename}`;
        compressedVideoSelect.appendChild(option);
    });
}

function showOriginalVideoSelection() {
    originalVideoSelect.style.display = 'block';
    selectOriginalVideoBtn.textContent = '🔄 Refresh List';
    refreshOriginalVideoList();
}

function showCompressedVideoSelection() {
    compressedVideoSelect.style.display = 'block';
    selectCompressedVideoBtn.textContent = '🔄 Refresh List';
    refreshCompressedVideoList();
}

async function loadSelectedOriginalVideo() {
    const selectedValue = originalVideoSelect.value;
    if (selectedValue) {
        const videoInfo = JSON.parse(selectedValue);
        await loadVideoIntoPlayer(videoInfo, 'original');
    }
}

async function loadSelectedCompressedVideo() {
    const selectedValue = compressedVideoSelect.value;
    if (selectedValue) {
        const videoInfo = JSON.parse(selectedValue);
        await loadVideoIntoPlayer(videoInfo, 'compressed');
    }
}

// Test HLS playback function
async function testHLSPlayback() {
    console.log('Testing HLS playback...');

    // Create a test HLS video info object
    const testVideoInfo = {
        filename: 'auto_test_live_demo.m3u8',
        path: 's3://zmt-compressed-video/hls/auto_test_live_demo/playlist.m3u8',
        url: '/api/video/hls/zmt-compressed-video/auto_test_live_demo/playlist.m3u8',
        size: 121,
        type: 'HLS Stream',
        isY4M: false,
        directory: 'hls',
        streamName: 'auto_test_live_demo'
    };

    console.log('Loading test HLS stream:', testVideoInfo);
    await loadVideoIntoPlayer(testVideoInfo, 'compressed');
}

async function loadVideoIntoPlayer(videoInfo, playerType) {
    const isOriginal = playerType === 'original';
    const video = isOriginal ? originalVideo : compressedVideo;
    const videoInfo_element = isOriginal ? originalVideoInfo : compressedVideoInfo;
    const placeholder = isOriginal ? originalVideoPlaceholder : compressedVideoPlaceholder;

    // Check if this is an HLS stream
    const isHLSStream = videoInfo.url && (videoInfo.url.includes('/hls/') || videoInfo.type === 'HLS Stream') && videoInfo.url.endsWith('.m3u8');

    console.log('Video detection:', {
        filename: videoInfo.filename,
        url: videoInfo.url,
        type: videoInfo.type,
        isHLSStream: isHLSStream
    });

    // Update video info
    const fileType = videoInfo.isY4M ? ' (Y4M)' :
                    isHLSStream ? ' (HLS Stream)' :
                    ` (${videoInfo.filename.split('.').pop().toUpperCase()})`;
    const previewNote = videoInfo.isY4M ?
        '<div><strong>Note:</strong> Showing web-compatible preview. Original Y4M preserved.</div>' :
        isHLSStream ? '<div><strong>Note:</strong> Live HLS stream - updates in real-time as segments are uploaded.</div>' : '';

    // For HLS streams, get enhanced size information
    if (isHLSStream) {
        await updateVideoInfoWithHLSSize(videoInfo_element, videoInfo, fileType, previewNote);
    } else {
        videoInfo_element.innerHTML = `
            <div><strong>Filename:</strong> ${videoInfo.filename}${fileType}</div>
            <div><strong>Path:</strong> ${videoInfo.path}</div>
            ${videoInfo.size ? `<div><strong>Size:</strong> ${formatFileSize(videoInfo.size)}</div>` : ''}
            ${videoInfo.s3Uri ? `<div><strong>S3 URI:</strong> ${videoInfo.s3Uri}</div>` : ''}
            ${previewNote}
        `;
    }

    const videoUrl = videoInfo.url;
    console.log(`Loading ${playerType} video:`, videoInfo.filename, 'URL:', videoUrl, 'HLS:', isHLSStream);

    if (videoUrl) {
        if (isHLSStream && typeof Hls !== 'undefined' && Hls.isSupported()) {
            // Use HLS.js for HLS streams
            console.log('Using HLS.js for stream:', videoUrl);
            loadHLSVideo(video, videoUrl, videoInfo, isOriginal);
        } else if (isHLSStream && video.canPlayType('application/vnd.apple.mpegurl')) {
            // Native HLS support (Safari)
            console.log('Using native HLS support for stream:', videoUrl);
            video.src = videoUrl;
            setupStandardVideo(video, videoInfo, placeholder, isOriginal);
        } else if (!isHLSStream) {
            // Standard video file
            video.src = videoUrl;
            setupStandardVideo(video, videoInfo, placeholder, isOriginal);
        } else {
            // HLS not supported - show error with more details
            const hlsSupported = typeof Hls !== 'undefined' ? Hls.isSupported() : false;
            const nativeSupported = video.canPlayType('application/vnd.apple.mpegurl');

            console.error('HLS not supported:', {
                hlsJsAvailable: typeof Hls !== 'undefined',
                hlsJsSupported: hlsSupported,
                nativeSupported: nativeSupported,
                url: videoUrl
            });

            placeholder.innerHTML = `
                <div class="placeholder-content">
                    <div class="placeholder-icon">❌</div>
                    <p>HLS streaming not supported</p>
                    <small>HLS.js: ${typeof Hls !== 'undefined' ? 'Available' : 'Not loaded'}<br>
                    Native: ${nativeSupported ? 'Supported' : 'Not supported'}</small>
                </div>
            `;
            video.style.display = 'none';
            placeholder.style.display = 'flex';
            return;
        }
    } else {
        // No URL available - show error
        placeholder.innerHTML = `
            <div class="placeholder-content">
                <div class="placeholder-icon">❌</div>
                <p>No playable video URL</p>
                <small>File: ${videoInfo.filename}</small>
            </div>
        `;
        video.style.display = 'none';
        placeholder.style.display = 'flex';
    }

    // Update currentVideo if loading into original player
    if (isOriginal) {
        currentVideo = videoInfo;
    }
}

// Helper function to update video info with HLS size information
async function updateVideoInfoWithHLSSize(videoInfo_element, videoInfo, fileType, previewNote) {
    let sizeInfo = '';
    let additionalInfo = '';
    let pathInfo = videoInfo.path;

    try {
        const response = await fetch(`/api/compression/file-info/${encodeURIComponent(videoInfo.filename)}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.size) {
                if (data.segmentCount) {
                    // For HLS streams, show total size and segment count
                    sizeInfo = `<div><strong>Total Size:</strong> ${formatFileSize(data.size)}</div>`;
                    additionalInfo = `<div><strong>Segments:</strong> ${data.segmentCount} files</div>`;
                    pathInfo = `HLS Stream (${data.segmentCount} segments)`;
                } else {
                    // Fallback to regular size display
                    sizeInfo = `<div><strong>Size:</strong> ${formatFileSize(data.size)}</div>`;
                }
            }
        }
    } catch (error) {
        console.error('Failed to get HLS size info:', error);
        // Fallback to original size if available
        if (videoInfo.size) {
            sizeInfo = `<div><strong>Size:</strong> ${formatFileSize(videoInfo.size)}</div>`;
        }
    }

    videoInfo_element.innerHTML = `
        <div><strong>Filename:</strong> ${videoInfo.filename}${fileType}</div>
        <div><strong>Path:</strong> ${pathInfo}</div>
        ${sizeInfo}
        ${additionalInfo}
        ${videoInfo.s3Uri ? `<div><strong>S3 URI:</strong> ${videoInfo.s3Uri}</div>` : ''}
        ${previewNote}
    `;
}

// Helper function to load HLS video using HLS.js
function loadHLSVideo(video, url, videoInfo, isOriginal) {
    const placeholder = isOriginal ? originalVideoPlaceholder : compressedVideoPlaceholder;

    // Clean up any existing HLS instance
    if (video.hlsInstance) {
        video.hlsInstance.destroy();
        video.hlsInstance = null;
    }

    const hls = new Hls({
        debug: false,
        enableWorker: true,
        lowLatencyMode: false,  // Disable low latency for complete playback
        backBufferLength: 90,
        maxBufferLength: 600,   // Allow up to 10 minutes of buffering
        maxMaxBufferLength: 1200, // Maximum buffer length
        liveSyncDurationCount: 3,
        liveMaxLatencyDurationCount: 10,
        maxBufferSize: 60 * 1000 * 1000, // 60MB buffer
        maxBufferHole: 0.5,
        manifestLoadingTimeOut: 10000,
        manifestLoadingMaxRetry: 4,
        levelLoadingTimeOut: 10000,
        levelLoadingMaxRetry: 4,
        fragLoadingTimeOut: 20000,
        fragLoadingMaxRetry: 6
    });

    // Store HLS instance on video element for cleanup
    video.hlsInstance = hls;

    hls.loadSource(url);
    hls.attachMedia(video);

    hls.on(Hls.Events.MANIFEST_PARSED, function() {
        console.log('HLS manifest parsed successfully:', videoInfo.filename);
        video.style.display = 'block';
        placeholder.style.display = 'none';

        // Auto-play for compressed videos
        if (!isOriginal) {
            video.play().catch(e => {
                console.log('Auto-play prevented by browser policy');
            });
        }
    });

    hls.on(Hls.Events.ERROR, function(event, data) {
        console.error('HLS error:', data);
        if (data.fatal) {
            switch(data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                    console.log('Fatal network error encountered, trying to recover...');
                    hls.startLoad();
                    break;
                case Hls.ErrorTypes.MEDIA_ERROR:
                    console.log('Fatal media error encountered, trying to recover...');
                    hls.recoverMediaError();
                    break;
                default:
                    console.log('Fatal error, cannot recover');
                    placeholder.innerHTML = `
                        <div class="placeholder-content">
                            <div class="placeholder-icon">❌</div>
                            <p>HLS stream error</p>
                            <small>Failed to load: ${videoInfo.filename}</small>
                        </div>
                    `;
                    video.style.display = 'none';
                    placeholder.style.display = 'flex';
                    hls.destroy();
                    break;
            }
        }
    });

    // Handle live stream updates
    hls.on(Hls.Events.LEVEL_UPDATED, function(event, data) {
        console.log('HLS level updated - new segments available');
    });
}

// Helper function to setup standard video
function setupStandardVideo(video, videoInfo, placeholder, isOriginal) {
    video.style.display = 'block';
    placeholder.style.display = 'none';

    // Add error handling
    video.onerror = function() {
        console.error(`Failed to load video:`, video.src);
        placeholder.innerHTML = `
            <div class="placeholder-content">
                <div class="placeholder-icon">❌</div>
                <p>Failed to load video</p>
                <small>File: ${videoInfo.filename}</small>
            </div>
        `;
        video.style.display = 'none';
        placeholder.style.display = 'flex';
    };

    // Add load success handler
    video.onloadeddata = function() {
        console.log(`Video loaded successfully:`, videoInfo.filename);

        // Auto-play for compressed videos
        if (!isOriginal) {
            video.play().catch(e => {
                console.log('Auto-play prevented by browser policy');
            });
        }
    };
}

// Load compressed video
function loadCompressedVideo(outputPath) {
    const filename = outputPath.split('/').pop();
    const url = `/compressed/${filename}`;
    const ext = filename.split('.').pop().toLowerCase();
    const isY4M = ext === 'y4m';

    // For Y4M compressed files, check if preview exists
    let displayUrl = url;
    let previewNote = '';

    if (isY4M) {
        const previewFilename = filename.replace('.y4m', '_preview.mp4');
        const previewUrl = `/compressed/${previewFilename}`;

        // Check if preview exists by trying to load it
        checkFileExists(previewUrl).then(exists => {
            if (exists) {
                compressedVideo.src = previewUrl;
                previewNote = '<div><strong>Note:</strong> Showing web-compatible preview of compressed Y4M file.</div>';
            } else {
                // Create preview for compressed Y4M file
                createCompressedPreview(filename);
                compressedVideoPlaceholder.innerHTML = `
                    <div class="placeholder-content">
                        <div class="placeholder-icon">⚡</div>
                        <p>Compressed Y4M file ready</p>
                        <small>Generating web preview...</small>
                    </div>
                `;
                compressedVideo.style.display = 'none';
                compressedVideoPlaceholder.style.display = 'flex';
                return;
            }

            updateCompressedVideoInfo(filename, outputPath, previewNote);
        });
    } else {
        // MP4 or other web-compatible format
        compressedVideo.src = displayUrl;
        updateCompressedVideoInfo(filename, outputPath, previewNote);
    }
}

// Load compressed video from file info object
function loadCompressedVideoFromInfo(videoInfo) {
    const { filename, path: filePath, url, size, isY4M } = videoInfo;

    // Compressed files should always be MP4 (web-compatible)
    let videoUrl = url;
    let previewNote = '';

    // Since compression outputs MP4, we shouldn't need previews for compressed files
    if (filename.includes('_zmt.mp4')) {
        previewNote = '<div><strong>Format:</strong> Compressed MP4 (Web Compatible)</div>';
    }

    console.log('Loading compressed video:', filename, 'URL:', videoUrl);

    // Load the video
    if (videoUrl) {
        compressedVideo.src = videoUrl;
        compressedVideo.style.display = 'block';
        compressedVideoPlaceholder.style.display = 'none';

        // Add error handling
        compressedVideo.onerror = function() {
            console.error('Failed to load compressed video:', videoUrl);
            compressedVideoPlaceholder.innerHTML = `
                <div class="placeholder-content">
                    <div class="placeholder-icon">❌</div>
                    <p>Failed to load compressed video</p>
                    <small>File: ${filename}</small>
                </div>
            `;
            compressedVideo.style.display = 'none';
            compressedVideoPlaceholder.style.display = 'flex';
        };

        // Add load success handler
        compressedVideo.onloadeddata = function() {
            console.log('Compressed video loaded successfully:', filename);
            // Auto-play the compressed video for immediate comparison
            compressedVideo.play().catch(e => {
                console.log('Auto-play prevented by browser policy');
            });
        };

        // Update video info
        updateCompressedVideoInfo(filename, filePath, previewNote);
    } else {
        // Fallback to placeholder
        compressedVideoPlaceholder.innerHTML = `
            <div class="placeholder-content">
                <div class="placeholder-icon">❌</div>
                <p>No compressed video URL</p>
                <small>File: ${filename}</small>
            </div>
        `;
        compressedVideo.style.display = 'none';
        compressedVideoPlaceholder.style.display = 'flex';
    }
}

// Helper function to update compressed video info
async function updateCompressedVideoInfo(filename, outputPath, previewNote = '') {
    const ext = filename.split('.').pop().toLowerCase();
    const isHLSStream = ext === 'm3u8';
    const fileType = ext === 'y4m' ? ' (Y4M - Compressed)' :
                    isHLSStream ? ' (HLS Stream)' :
                    ` (${ext.toUpperCase()})`;

    // Get file size and additional info
    let sizeInfo = '';
    let additionalInfo = '';
    try {
        const response = await fetch(`/api/compression/file-info/${encodeURIComponent(filename)}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.size) {
                if (isHLSStream && data.segmentCount) {
                    // For HLS streams, show total size and segment count
                    sizeInfo = `<div><strong>Total Size:</strong> ${formatFileSize(data.size)}</div>`;
                    additionalInfo = `<div><strong>Segments:</strong> ${data.segmentCount} files</div>`;
                } else {
                    // For regular files, show file size
                    sizeInfo = `<div><strong>Size:</strong> ${formatFileSize(data.size)}</div>`;
                }
            }
        }
    } catch (error) {
        console.error('Failed to get file size:', error);
    }

    compressedVideoInfo.innerHTML = `
        <div><strong>Filename:</strong> ${filename}${fileType}</div>
        <div><strong>Path:</strong> ${outputPath}</div>
        ${sizeInfo}
        ${additionalInfo}
        ${previewNote}
    `;

    compressedVideo.style.display = 'block';
    compressedVideoPlaceholder.style.display = 'none';

    // Auto-play the compressed video for immediate comparison
    compressedVideo.play().catch(e => {
        console.log('Auto-play prevented by browser policy');
    });
}

// Check if file exists
async function checkFileExists(url) {
    try {
        const response = await fetch(url, { method: 'HEAD' });
        return response.ok;
    } catch (error) {
        return false;
    }
}

// Create preview for compressed Y4M file
async function createCompressedPreview(filename) {
    try {
        const response = await fetch('/api/compression/create-preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ filename: filename })
        });

        const result = await response.json();
        if (result.success) {
            // Reload the compressed video with preview
            setTimeout(() => {
                const previewUrl = `/compressed/${filename.replace('.y4m', '_preview.mp4')}`;
                compressedVideo.src = previewUrl;
                updateCompressedVideoInfo(filename, `compressed/${filename}`,
                    '<div><strong>Note:</strong> Showing web-compatible preview of compressed Y4M file.</div>');
            }, 2000); // Wait a bit for preview generation
        }
    } catch (error) {
        console.error('Failed to create compressed preview:', error);
        // Show placeholder with error message
        compressedVideoPlaceholder.innerHTML = `
            <div class="placeholder-content">
                <div class="placeholder-icon">⚠️</div>
                <p>Y4M file compressed successfully</p>
                <small>Preview generation failed - file playable via download</small>
            </div>
        `;
    }
}



// Start batch compression
async function startBatchCompression() {
    const inputPath = batchInputPath.value.trim();
    const outputPath = batchOutputPath.value.trim();
    
    // Get regions if S3 paths are used
    const inputRegionValue = inputPath.startsWith('s3://') ? inputRegion.value : null;
    const outputRegionValue = outputPath.startsWith('s3://') ? outputRegion.value : null;

    if (!inputPath || !outputPath) {
        showError('Both input and output paths are required for batch compression');
        return;
    }

    // Validate paths before starting compression
    showStatus('Validating paths...', 'running');

    try {
        // Validate paths
        // ...existing validation code...

        showStatus('Paths validated. Starting compression...', 'running');

        const response = await fetch('/api/compression/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                inputPath: inputPath,
                outputPath: outputPath,
                inputRegion: inputRegionValue,
                outputRegion: outputRegionValue,
                mode: 'batch'
            })
        });

        const result = await response.json();
        if (result.success) {
            currentCompressionJob = result.compressionId;
            runBatchCompressionBtn.disabled = true;
            cancelCompressionBtn.disabled = false;

            // Show logs immediately and clear previous content
            compressionLogs.style.display = 'block';
            logsContent.innerHTML = '';
            showCompressionLogs();
        } else {
            showError(result.error || 'Failed to start batch compression');
        }
    } catch (error) {
        showError(`Batch compression error: ${error.message}`);
    }
}

// Validate directory path
async function validatePath(dirPath) {
    try {
        // Check for ARN format and show helpful error
        if (dirPath.startsWith('arn:aws:s3:::')) {
            return {
                valid: false,
                message: 'ARN format not supported. Use S3 URI format: s3://bucket-name/path/'
            };
        }

        const response = await fetch('/api/video/validate-path', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ path: dirPath })
        });

        const result = await response.json();
        return result;
    } catch (error) {
        console.error('Path validation error:', error);
        return { valid: false, message: 'Failed to validate path' };
    }
}

// Create directory if it doesn't exist
async function createDirectory(dirPath) {
    try {
        const response = await fetch('/api/video/create-directory', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ path: dirPath })
        });

        const result = await response.json();
        return result;
    } catch (error) {
        console.error('Directory creation error:', error);
        return { success: false, message: 'Failed to create directory' };
    }
}

// Cancel compression
async function cancelCompression() {
    if (!currentCompressionJob) return;

    try {
        const response = await fetch(`/api/compression/cancel/${currentCompressionJob}`, {
            method: 'POST'
        });

        const result = await response.json();
        if (result.success) {
            showStatus('Compression cancelled', 'failed');
            resetCompressionUI();
        }
    } catch (error) {
        showError(`Cancel error: ${error.message}`);
    }
}

// Add event listeners for S3 path detection
function setupS3RegionControls() {
    // Show/hide region selectors based on path input
    batchInputPath.addEventListener('input', function() {
        const isS3Path = this.value.trim().startsWith('s3://');
        inputRegionGroup.style.display = isS3Path ? 'block' : 'none';
    });
    
    batchOutputPath.addEventListener('input', function() {
        const isS3Path = this.value.trim().startsWith('s3://');
        outputRegionGroup.style.display = isS3Path ? 'block' : 'none';
    });
}

// Initialize all event listeners
function initializeEventListeners() {
    // ...existing code...
    
    // Setup S3 region controls
    setupS3RegionControls();
}

// Update compression status
function updateCompressionStatus(status) {
    const statusText = `Status: ${status.status.toUpperCase()}`;
    let statusClass = '';

    switch (status.status) {
        case 'running':
        case 'starting':
            statusClass = 'status-running';
            break;
        case 'completed':
            statusClass = 'status-completed';
            resetCompressionUI();
            break;
        case 'failed':
        case 'cancelled':
            statusClass = 'status-failed';
            resetCompressionUI();
            break;
    }

    compressionStatus.innerHTML = statusText;
    compressionStatus.className = `compression-status ${statusClass}`;

    if (status.status === 'running' || status.status === 'starting') {
        compressionProgress.style.display = 'block';
    }
}

// Update progress
function updateProgress(progress) {
    progressFill.style.width = `${progress}%`;
    progressText.textContent = `${progress}%`;
}

// Show compression logs
function showCompressionLogs() {
    compressionLogs.style.display = 'block';

    // Poll for logs
    const pollLogs = async () => {
        if (!currentCompressionJob) return;

        try {
            const response = await fetch(`/api/compression/logs/${currentCompressionJob}`);
            const result = await response.json();

            if (result.success) {
                displayLogs(result.logs);
            }
        } catch (error) {
            console.error('Failed to fetch logs:', error);
        }

        // Continue polling if job is active
        if (currentCompressionJob) {
            setTimeout(pollLogs, 2000);
        }
    };

    pollLogs();
}

// Display logs
function displayLogs(logs) {
    logsContent.innerHTML = logs.map(log => {
        const logType = log.type || 'info';
        return `
            <div class="log-entry log-${logType}">
                <span class="log-timestamp">${new Date(log.timestamp).toLocaleTimeString()}</span>
                <span class="log-message">${log.message}</span>
            </div>
        `;
    }).join('');

    // Auto-scroll to bottom only if enabled
    if (autoScrollEnabled) {
        logsContent.scrollTop = logsContent.scrollHeight;
    }
}

// Setup log controls
function setupLogControls() {
    // Auto-scroll toggle
    autoScrollToggle.addEventListener('click', () => {
        autoScrollEnabled = !autoScrollEnabled;
        autoScrollToggle.textContent = autoScrollEnabled ? '📜 Auto-scroll: ON' : '📜 Auto-scroll: OFF';
        autoScrollToggle.className = autoScrollEnabled ? 'auto-scroll-toggle' : 'auto-scroll-toggle disabled';

        if (autoScrollEnabled) {
            // Scroll to bottom when re-enabled
            logsContent.scrollTop = logsContent.scrollHeight;
        }
    });

    // Clear logs button
    clearLogsBtn.addEventListener('click', () => {
        logsContent.innerHTML = '';
        appendLogEntry('Logs cleared', 'info');
    });
}

// Append single log entry (for real-time updates)
function appendLogEntry(message, type = 'info') {
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${type}`;
    logEntry.innerHTML = `
        <span class="log-timestamp">${new Date().toLocaleTimeString()}</span>
        <span class="log-message">${message}</span>
    `;

    logsContent.appendChild(logEntry);

    // Auto-scroll to bottom only if enabled
    if (autoScrollEnabled) {
        logsContent.scrollTop = logsContent.scrollHeight;
    }

    // Limit number of log entries to prevent memory issues
    const maxEntries = 1000;
    while (logsContent.children.length > maxEntries) {
        logsContent.removeChild(logsContent.firstChild);
    }
}

// Reset compression UI
function resetCompressionUI() {
    currentCompressionJob = null;

    // Reset compression buttons
    runBatchCompressionBtn.disabled = false;
    cancelCompressionBtn.disabled = true;

    // Reset progress and logs
    compressionProgress.style.display = 'none';
    progressFill.style.width = '0%';
    progressText.textContent = '0%';
}

// Utility functions
function showStatus(message, type) {
    compressionStatus.innerHTML = message;
    compressionStatus.className = `compression-status status-${type}`;
}

function showError(message) {
    compressionStatus.innerHTML = `Error: ${message}`;
    compressionStatus.className = 'compression-status status-failed';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Initialize video lists on page load
document.addEventListener('DOMContentLoaded', () => {
    refreshOriginalVideoList();
    refreshCompressedVideoList();
});

// Setup video synchronization
function setupVideoSync() {
    let syncEnabled = false;

    // Add sync button to the interface
    const syncButton = document.createElement('button');
    syncButton.id = 'syncVideosBtn';
    syncButton.className = 'secondary-btn';
    syncButton.textContent = '🔗 Sync Videos';
    syncButton.title = 'Synchronize playback between original and compressed videos';
    syncButton.style.display = 'none';

    // Add sync button to the compression controls
    const compressionControls = document.querySelector('.compression-controls');
    if (compressionControls) {
        compressionControls.appendChild(syncButton);
    }

    // Toggle sync functionality
    syncButton.addEventListener('click', () => {
        syncEnabled = !syncEnabled;
        syncButton.textContent = syncEnabled ? '🔗 Sync ON' : '🔗 Sync Videos';
        syncButton.className = syncEnabled ? 'primary-btn' : 'secondary-btn';

        if (syncEnabled) {
            showStatus('Video synchronization enabled', 'completed');
        } else {
            showStatus('Video synchronization disabled', 'running');
        }
    });

    // Sync original video events to compressed video
    originalVideo.addEventListener('play', () => {
        if (syncEnabled && compressedVideo.src && !compressedVideo.paused) {
            return; // Don't sync if compressed is already playing
        }
        if (syncEnabled && compressedVideo.src) {
            compressedVideo.currentTime = originalVideo.currentTime;
            compressedVideo.play().catch(e => console.log('Sync play failed'));
        }
    });

    originalVideo.addEventListener('pause', () => {
        if (syncEnabled && compressedVideo.src) {
            compressedVideo.pause();
        }
    });

    originalVideo.addEventListener('seeked', () => {
        if (syncEnabled && compressedVideo.src) {
            compressedVideo.currentTime = originalVideo.currentTime;
        }
    });

    // Sync compressed video events to original video
    compressedVideo.addEventListener('play', () => {
        if (syncEnabled && originalVideo.src && !originalVideo.paused) {
            return; // Don't sync if original is already playing
        }
        if (syncEnabled && originalVideo.src) {
            originalVideo.currentTime = compressedVideo.currentTime;
            originalVideo.play().catch(e => console.log('Sync play failed'));
        }
    });

    compressedVideo.addEventListener('pause', () => {
        if (syncEnabled && originalVideo.src) {
            originalVideo.pause();
        }
    });

    compressedVideo.addEventListener('seeked', () => {
        if (syncEnabled && originalVideo.src) {
            originalVideo.currentTime = compressedVideo.currentTime;
        }
    });

    // Show sync button when both videos are loaded
    function checkSyncButtonVisibility() {
        const bothVideosLoaded = originalVideo.src && compressedVideo.src;
        syncButton.style.display = bothVideosLoaded ? 'inline-block' : 'none';
    }

    // Monitor video loading
    originalVideo.addEventListener('loadeddata', checkSyncButtonVisibility);
    compressedVideo.addEventListener('loadeddata', checkSyncButtonVisibility);

    // Store sync state globally
    window.videoSyncEnabled = () => syncEnabled;
}
