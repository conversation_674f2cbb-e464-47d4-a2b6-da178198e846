// Initialize Socket.IO
const socket = io();

// Global variables
let currentVideo = null;
let currentCompressionJob = null;

// DOM elements
const fileInput = document.getElementById('fileInput');
const selectFileBtn = document.getElementById('selectFileBtn');
const selectedFileName = document.getElementById('selectedFileName');
const s3Uri = document.getElementById('s3Uri');
const loadS3Btn = document.getElementById('loadS3Btn');
const originalVideo = document.getElementById('originalVideo');
const originalVideoInfo = document.getElementById('originalVideoInfo');
const originalVideoPlaceholder = document.getElementById('originalVideoPlaceholder');
const compressedVideo = document.getElementById('compressedVideo');
const compressedVideoInfo = document.getElementById('compressedVideoInfo');
const compressedVideoPlaceholder = document.getElementById('compressedVideoPlaceholder');
const runSingleCompressionBtn = document.getElementById('runSingleCompressionBtn');
const runBatchCompressionBtn = document.getElementById('runBatchCompressionBtn');
const cancelCompressionBtn = document.getElementById('cancelCompressionBtn');
const batchInputPath = document.getElementById('batchInputPath');
const batchOutputPath = document.getElementById('batchOutputPath');
const selectInputDirBtn = document.getElementById('selectInputDirBtn');
const selectOutputDirBtn = document.getElementById('selectOutputDirBtn');
const inputDirSelector = document.getElementById('inputDirSelector');
const outputDirSelector = document.getElementById('outputDirSelector');
const compressionStatus = document.getElementById('compressionStatus');
const compressionProgress = document.getElementById('compressionProgress');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const compressionLogs = document.getElementById('compressionLogs');
const logsContent = document.getElementById('logsContent');
const refreshCompressedBtn = document.getElementById('refreshCompressedBtn');
const compressedFilesList = document.getElementById('compressedFilesList');

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    addPathValidation();
    setupVideoSync();
});

// Event listeners
function setupEventListeners() {
    // File input
    selectFileBtn.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFileSelect);

    // S3 input
    loadS3Btn.addEventListener('click', loadFromS3);
    s3Uri.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') loadFromS3();
    });

    // Directory selection
    selectInputDirBtn.addEventListener('click', () => inputDirSelector.click());
    selectOutputDirBtn.addEventListener('click', () => outputDirSelector.click());
    inputDirSelector.addEventListener('change', handleInputDirSelect);
    outputDirSelector.addEventListener('change', handleOutputDirSelect);

    // Compression controls
    runSingleCompressionBtn.addEventListener('click', startSingleCompression);
    runBatchCompressionBtn.addEventListener('click', startBatchCompression);
    cancelCompressionBtn.addEventListener('click', cancelCompression);
}

// Socket.IO event listeners
socket.on('compressionStatus', (status) => {
    updateCompressionStatus(status);
});

socket.on('compressionProgress', (data) => {
    updateProgress(data.progress);
});

socket.on('compressionComplete', (status) => {
    updateCompressionStatus(status);
    loadCompressedFiles();
    if (status.status === 'completed') {
        loadCompressedVideo(status.outputPath);
    }
});

socket.on('compressionError', (data) => {
    showError(`Compression failed: ${data.error}`);
});

socket.on('inputFilesDetected', (data) => {
    handleInputFilesDetected(data);
});

socket.on('outputFileCreated', (data) => {
    handleOutputFileCreated(data);
});

// File selection handler
function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        selectedFileName.textContent = file.name;
        uploadFile(file);
    }
}

// Directory selection handlers
function handleInputDirSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        const firstFile = files[0];
        const dirInfo = extractDirectoryInfo(firstFile);

        batchInputPath.value = dirInfo.path;
        showStatus(`Input directory selected: ${dirInfo.displayName} (${files.length} files)`, 'completed');

        // Add visual feedback
        selectInputDirBtn.style.background = '#218838';
        selectInputDirBtn.textContent = '✓';
        setTimeout(() => {
            selectInputDirBtn.style.background = '#28a745';
            selectInputDirBtn.textContent = '📁';
        }, 2000);
    }
}

function handleOutputDirSelect(e) {
    const files = e.target.files;
    if (files.length > 0) {
        const firstFile = files[0];
        const dirInfo = extractDirectoryInfo(firstFile);

        batchOutputPath.value = dirInfo.path;
        showStatus(`Output directory selected: ${dirInfo.displayName}`, 'completed');

        // Add visual feedback
        selectOutputDirBtn.style.background = '#218838';
        selectOutputDirBtn.textContent = '✓';
        setTimeout(() => {
            selectOutputDirBtn.style.background = '#28a745';
            selectOutputDirBtn.textContent = '📁';
        }, 2000);
    }
}

// Helper function to extract directory information
function extractDirectoryInfo(file) {
    const fullPath = file.webkitRelativePath || file.name;
    const pathParts = fullPath.split('/');

    // Remove the filename to get just the directory
    pathParts.pop();

    // Get the directory name
    const dirName = pathParts[pathParts.length - 1] || 'selected_directory';

    // Create a relative path that the server can understand
    const relativePath = `./${dirName}`;

    return {
        path: relativePath,
        displayName: dirName,
        fullPath: pathParts.join('/'),
        depth: pathParts.length
    };
}

// Add real-time path validation
function addPathValidation() {
    // Add input event listeners for real-time validation
    batchInputPath.addEventListener('input', debounce(validateInputPath, 500));
    batchOutputPath.addEventListener('input', debounce(validateOutputPath, 500));
}

// Validate input path in real-time
async function validateInputPath() {
    const path = batchInputPath.value.trim();
    if (!path) return;

    const validation = await validatePath(path);
    updatePathValidationUI(batchInputPath, validation);
}

// Validate output path in real-time
async function validateOutputPath() {
    const path = batchOutputPath.value.trim();
    if (!path) return;

    const validation = await validatePath(path);
    updatePathValidationUI(batchOutputPath, validation);
}

// Update UI based on path validation
function updatePathValidationUI(inputElement, validation) {
    // Remove existing validation classes
    inputElement.classList.remove('path-valid', 'path-invalid', 'path-s3');

    if (validation.type === 's3') {
        inputElement.classList.add('path-s3');
    } else if (validation.valid) {
        inputElement.classList.add('path-valid');
    } else {
        inputElement.classList.add('path-invalid');
    }
}

// Debounce function for input validation
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Upload file to server
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('video', file);

    try {
        showStatus('Uploading file...', 'running');
        const response = await fetch('/api/video/upload', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        if (result.success) {
            showStatus('File uploaded successfully!', 'completed');
            loadVideo(result.video);
            loadLocalFiles();
        } else {
            showError(result.error || 'Upload failed');
        }
    } catch (error) {
        showError(`Upload error: ${error.message}`);
    }
}

// Load video from S3
async function loadFromS3() {
    const uri = s3Uri.value.trim();
    if (!uri) {
        showError('Please enter an S3 URI');
        return;
    }

    try {
        showStatus('Loading from S3...', 'running');
        const response = await fetch('/api/video/s3', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ s3Uri: uri })
        });

        const result = await response.json();
        if (result.success) {
            showStatus('Video loaded from S3 successfully!', 'completed');
            loadVideo(result.video);
            s3Uri.value = '';
        } else {
            showError(result.error || 'S3 load failed');
        }
    } catch (error) {
        showError(`S3 error: ${error.message}`);
    }
}

// Load video into player
function loadVideo(videoInfo) {
    currentVideo = videoInfo;

    // Update video info
    const fileType = videoInfo.isY4M ? ' (Y4M - Original)' : '';
    const previewNote = videoInfo.isY4M && videoInfo.previewUrl ?
        '<div><strong>Note:</strong> Showing web-compatible preview. Original Y4M preserved for compression.</div>' : '';

    originalVideoInfo.innerHTML = `
        <div><strong>Filename:</strong> ${videoInfo.filename}${fileType}</div>
        <div><strong>Path:</strong> ${videoInfo.path}</div>
        ${videoInfo.size ? `<div><strong>Size:</strong> ${formatFileSize(videoInfo.size)}</div>` : ''}
        ${videoInfo.s3Uri ? `<div><strong>S3 URI:</strong> ${videoInfo.s3Uri}</div>` : ''}
        ${previewNote}
    `;

    // Load video - use preview URL for Y4M files, original URL for others
    const videoUrl = (videoInfo.isY4M && videoInfo.previewUrl) ? videoInfo.previewUrl : videoInfo.url;

    if (videoUrl) {
        originalVideo.src = videoUrl;
        originalVideo.style.display = 'block';
        originalVideoPlaceholder.style.display = 'none';
    } else if (videoInfo.isY4M) {
        // Y4M file without preview - show message
        originalVideoPlaceholder.innerHTML = `
            <div class="placeholder-content">
                <div class="placeholder-icon">🎬</div>
                <p>Y4M file loaded</p>
                <small>Preview generation in progress...</small>
            </div>
        `;
        originalVideo.style.display = 'none';
        originalVideoPlaceholder.style.display = 'flex';
    }

    // Enable single file compression button
    runSingleCompressionBtn.disabled = false;
}

// Load compressed video
function loadCompressedVideo(outputPath) {
    const filename = outputPath.split('/').pop();
    const url = `/compressed/${filename}`;
    const ext = filename.split('.').pop().toLowerCase();
    const isY4M = ext === 'y4m';

    // For Y4M compressed files, check if preview exists
    let displayUrl = url;
    let previewNote = '';

    if (isY4M) {
        const previewFilename = filename.replace('.y4m', '_preview.mp4');
        const previewUrl = `/compressed/${previewFilename}`;

        // Check if preview exists by trying to load it
        checkFileExists(previewUrl).then(exists => {
            if (exists) {
                compressedVideo.src = previewUrl;
                previewNote = '<div><strong>Note:</strong> Showing web-compatible preview of compressed Y4M file.</div>';
            } else {
                // Create preview for compressed Y4M file
                createCompressedPreview(filename);
                compressedVideoPlaceholder.innerHTML = `
                    <div class="placeholder-content">
                        <div class="placeholder-icon">⚡</div>
                        <p>Compressed Y4M file ready</p>
                        <small>Generating web preview...</small>
                    </div>
                `;
                compressedVideo.style.display = 'none';
                compressedVideoPlaceholder.style.display = 'flex';
                return;
            }

            updateCompressedVideoInfo(filename, outputPath, previewNote);
        });
    } else {
        // MP4 or other web-compatible format
        compressedVideo.src = displayUrl;
        updateCompressedVideoInfo(filename, outputPath, previewNote);
    }
}

// Helper function to update compressed video info
async function updateCompressedVideoInfo(filename, outputPath, previewNote = '') {
    const ext = filename.split('.').pop().toLowerCase();
    const fileType = ext === 'y4m' ? ' (Y4M - Compressed)' : ` (${ext.toUpperCase()})`;

    // Get file size
    let sizeInfo = '';
    try {
        const response = await fetch(`/api/compression/file-info/${encodeURIComponent(filename)}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.size) {
                sizeInfo = `<div><strong>Size:</strong> ${formatFileSize(data.size)}</div>`;
            }
        }
    } catch (error) {
        console.error('Failed to get file size:', error);
    }

    compressedVideoInfo.innerHTML = `
        <div><strong>Filename:</strong> ${filename}${fileType}</div>
        <div><strong>Path:</strong> ${outputPath}</div>
        ${sizeInfo}
        ${previewNote}
    `;

    compressedVideo.style.display = 'block';
    compressedVideoPlaceholder.style.display = 'none';

    // Auto-play the compressed video for immediate comparison
    compressedVideo.play().catch(e => {
        console.log('Auto-play prevented by browser policy');
    });
}

// Check if file exists
async function checkFileExists(url) {
    try {
        const response = await fetch(url, { method: 'HEAD' });
        return response.ok;
    } catch (error) {
        return false;
    }
}

// Create preview for compressed Y4M file
async function createCompressedPreview(filename) {
    try {
        const response = await fetch('/api/compression/create-preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ filename: filename })
        });

        const result = await response.json();
        if (result.success) {
            // Reload the compressed video with preview
            setTimeout(() => {
                const previewUrl = `/compressed/${filename.replace('.y4m', '_preview.mp4')}`;
                compressedVideo.src = previewUrl;
                updateCompressedVideoInfo(filename, `compressed/${filename}`,
                    '<div><strong>Note:</strong> Showing web-compatible preview of compressed Y4M file.</div>');
            }, 2000); // Wait a bit for preview generation
        }
    } catch (error) {
        console.error('Failed to create compressed preview:', error);
        // Show placeholder with error message
        compressedVideoPlaceholder.innerHTML = `
            <div class="placeholder-content">
                <div class="placeholder-icon">⚠️</div>
                <p>Y4M file compressed successfully</p>
                <small>Preview generation failed - file playable via download</small>
            </div>
        `;
    }
}

// Start single file compression
async function startSingleCompression() {
    if (!currentVideo) {
        showError('No video loaded');
        return;
    }

    try {
        const response = await fetch('/api/compression/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                inputPath: currentVideo.path,
                isS3: !!currentVideo.s3Uri,
                mode: 'single'
            })
        });

        const result = await response.json();
        if (result.success) {
            currentCompressionJob = result.compressionId;
            runSingleCompressionBtn.disabled = true;
            cancelCompressionBtn.disabled = false;
            showCompressionLogs();
        } else {
            showError(result.error || 'Failed to start compression');
        }
    } catch (error) {
        showError(`Compression error: ${error.message}`);
    }
}

// Start batch compression
async function startBatchCompression() {
    const inputPath = batchInputPath.value.trim();
    const outputPath = batchOutputPath.value.trim();

    if (!inputPath || !outputPath) {
        showError('Both input and output paths are required for batch compression');
        return;
    }

    // Validate paths before starting compression
    showStatus('Validating paths...', 'running');

    try {
        // Validate input path
        const inputValidation = await validatePath(inputPath);
        if (!inputValidation.valid && !inputPath.startsWith('s3://')) {
            showError(`Invalid input path: ${inputValidation.message}`);
            return;
        }

        // Validate output path (for local paths)
        if (!outputPath.startsWith('s3://')) {
            const outputValidation = await validatePath(outputPath);
            if (!outputValidation.valid) {
                // Try to create the directory if it doesn't exist
                const createResult = await createDirectory(outputPath);
                if (!createResult.success) {
                    showError(`Invalid output path: ${outputValidation.message}`);
                    return;
                }
            }
        }

        showStatus('Paths validated. Starting compression...', 'running');

        const response = await fetch('/api/compression/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                inputPath: inputPath,
                outputPath: outputPath,
                mode: 'batch'
            })
        });

        const result = await response.json();
        if (result.success) {
            currentCompressionJob = result.compressionId;
            runBatchCompressionBtn.disabled = true;
            runSingleCompressionBtn.disabled = true;
            cancelCompressionBtn.disabled = false;
            showCompressionLogs();
        } else {
            showError(result.error || 'Failed to start batch compression');
        }
    } catch (error) {
        showError(`Batch compression error: ${error.message}`);
    }
}

// Validate directory path
async function validatePath(dirPath) {
    try {
        const response = await fetch('/api/video/validate-path', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ path: dirPath })
        });

        const result = await response.json();
        return result;
    } catch (error) {
        console.error('Path validation error:', error);
        return { valid: false, message: 'Failed to validate path' };
    }
}

// Create directory if it doesn't exist
async function createDirectory(dirPath) {
    try {
        const response = await fetch('/api/video/create-directory', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ path: dirPath })
        });

        const result = await response.json();
        return result;
    } catch (error) {
        console.error('Directory creation error:', error);
        return { success: false, message: 'Failed to create directory' };
    }
}

// Cancel compression
async function cancelCompression() {
    if (!currentCompressionJob) return;

    try {
        const response = await fetch(`/api/compression/cancel/${currentCompressionJob}`, {
            method: 'POST'
        });

        const result = await response.json();
        if (result.success) {
            showStatus('Compression cancelled', 'failed');
            resetCompressionUI();
        }
    } catch (error) {
        showError(`Cancel error: ${error.message}`);
    }
}





// Update compression status
function updateCompressionStatus(status) {
    const statusText = `Status: ${status.status.toUpperCase()}`;
    let statusClass = '';

    switch (status.status) {
        case 'running':
        case 'starting':
            statusClass = 'status-running';
            break;
        case 'completed':
            statusClass = 'status-completed';
            resetCompressionUI();
            break;
        case 'failed':
        case 'cancelled':
            statusClass = 'status-failed';
            resetCompressionUI();
            break;
    }

    compressionStatus.innerHTML = statusText;
    compressionStatus.className = `compression-status ${statusClass}`;

    if (status.status === 'running' || status.status === 'starting') {
        compressionProgress.style.display = 'block';
    }
}

// Update progress
function updateProgress(progress) {
    progressFill.style.width = `${progress}%`;
    progressText.textContent = `${progress}%`;
}

// Show compression logs
function showCompressionLogs() {
    compressionLogs.style.display = 'block';

    // Poll for logs
    const pollLogs = async () => {
        if (!currentCompressionJob) return;

        try {
            const response = await fetch(`/api/compression/logs/${currentCompressionJob}`);
            const result = await response.json();

            if (result.success) {
                displayLogs(result.logs);
            }
        } catch (error) {
            console.error('Failed to fetch logs:', error);
        }

        // Continue polling if job is active
        if (currentCompressionJob) {
            setTimeout(pollLogs, 2000);
        }
    };

    pollLogs();
}

// Display logs
function displayLogs(logs) {
    logsContent.innerHTML = logs.map(log => `
        <div class="log-entry">
            <span class="log-timestamp">${new Date(log.timestamp).toLocaleTimeString()}</span>
            <span class="log-message">${log.message}</span>
        </div>
    `).join('');

    // Scroll to bottom
    logsContent.scrollTop = logsContent.scrollHeight;
}

// Reset compression UI
function resetCompressionUI() {
    currentCompressionJob = null;

    // Reset compression buttons
    runSingleCompressionBtn.disabled = !currentVideo;
    runBatchCompressionBtn.disabled = false;
    cancelCompressionBtn.disabled = true;

    // Reset progress and logs
    compressionProgress.style.display = 'none';
    progressFill.style.width = '0%';
    progressText.textContent = '0%';
}

// Utility functions
function showStatus(message, type) {
    compressionStatus.innerHTML = message;
    compressionStatus.className = `compression-status status-${type}`;
}

function showError(message) {
    compressionStatus.innerHTML = `Error: ${message}`;
    compressionStatus.className = 'compression-status status-failed';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Handle input files detected
function handleInputFilesDetected(data) {
    const { jobId, inputFiles, mode } = data;

    if (inputFiles.length > 0) {
        // Load the first input file into the original video player
        const firstFile = inputFiles[0];

        // Create video info object
        const videoInfo = {
            filename: firstFile.filename,
            path: firstFile.path,
            url: firstFile.url,
            size: firstFile.size,
            isY4M: firstFile.filename.toLowerCase().endsWith('.y4m'),
            compressionMode: mode
        };

        // Load into original video player with compression context
        loadVideoForCompression(videoInfo);

        // Show status message
        if (mode === 'single') {
            showStatus(`Loading input file: ${firstFile.filename}`, 'running');
        } else {
            showStatus(`Loading first of ${inputFiles.length} input files: ${firstFile.filename}`, 'running');
        }
    }
}

// Handle output file created
function handleOutputFileCreated(data) {
    const { jobId, outputFile, mode } = data;

    // Load the output file into the compressed video player
    loadCompressedVideo(outputFile.path);

    // Show status message
    showStatus(`Compressed video ready: ${outputFile.filename}`, 'completed');

    // If it's a single file compression, we can show completion
    if (mode === 'single') {
        // The compression is essentially complete for viewing purposes
        updateProgress(100);
    }
}

// Enhanced loadVideo function to handle compression context
function loadVideoForCompression(videoInfo) {
    currentVideo = videoInfo;

    // Update video info with compression context
    const fileType = videoInfo.isY4M ? ' (Y4M - Original)' : '';
    const compressionNote = videoInfo.compressionMode ?
        `<div><strong>Compression Mode:</strong> ${videoInfo.compressionMode}</div>` : '';
    const previewNote = videoInfo.isY4M && videoInfo.previewUrl ?
        '<div><strong>Note:</strong> Showing web-compatible preview. Original Y4M preserved for compression.</div>' : '';

    originalVideoInfo.innerHTML = `
        <div><strong>Filename:</strong> ${videoInfo.filename}${fileType}</div>
        <div><strong>Path:</strong> ${videoInfo.path}</div>
        ${videoInfo.size ? `<div><strong>Size:</strong> ${formatFileSize(videoInfo.size)}</div>` : ''}
        ${videoInfo.s3Uri ? `<div><strong>S3 URI:</strong> ${videoInfo.s3Uri}</div>` : ''}
        ${compressionNote}
        ${previewNote}
    `;

    // Load video - use preview URL for Y4M files, original URL for others
    const videoUrl = (videoInfo.isY4M && videoInfo.previewUrl) ? videoInfo.previewUrl : videoInfo.url;

    if (videoUrl) {
        originalVideo.src = videoUrl;
        originalVideo.style.display = 'block';
        originalVideoPlaceholder.style.display = 'none';

        // Auto-play the video for immediate preview
        originalVideo.play().catch(e => {
            console.log('Auto-play prevented by browser policy');
        });
    } else if (videoInfo.isY4M) {
        // Y4M file without preview - show message
        originalVideoPlaceholder.innerHTML = `
            <div class="placeholder-content">
                <div class="placeholder-icon">🎬</div>
                <p>Y4M file loaded</p>
                <small>Preview generation in progress...</small>
            </div>
        `;
        originalVideo.style.display = 'none';
        originalVideoPlaceholder.style.display = 'flex';
    }
}

// Setup video synchronization
function setupVideoSync() {
    let syncEnabled = false;

    // Add sync button to the interface
    const syncButton = document.createElement('button');
    syncButton.id = 'syncVideosBtn';
    syncButton.className = 'secondary-btn';
    syncButton.textContent = '🔗 Sync Videos';
    syncButton.title = 'Synchronize playback between original and compressed videos';
    syncButton.style.display = 'none';

    // Add sync button to the compression controls
    const compressionControls = document.querySelector('.compression-controls');
    if (compressionControls) {
        compressionControls.appendChild(syncButton);
    }

    // Toggle sync functionality
    syncButton.addEventListener('click', () => {
        syncEnabled = !syncEnabled;
        syncButton.textContent = syncEnabled ? '🔗 Sync ON' : '🔗 Sync Videos';
        syncButton.className = syncEnabled ? 'primary-btn' : 'secondary-btn';

        if (syncEnabled) {
            showStatus('Video synchronization enabled', 'completed');
        } else {
            showStatus('Video synchronization disabled', 'running');
        }
    });

    // Sync original video events to compressed video
    originalVideo.addEventListener('play', () => {
        if (syncEnabled && compressedVideo.src && !compressedVideo.paused) {
            return; // Don't sync if compressed is already playing
        }
        if (syncEnabled && compressedVideo.src) {
            compressedVideo.currentTime = originalVideo.currentTime;
            compressedVideo.play().catch(e => console.log('Sync play failed'));
        }
    });

    originalVideo.addEventListener('pause', () => {
        if (syncEnabled && compressedVideo.src) {
            compressedVideo.pause();
        }
    });

    originalVideo.addEventListener('seeked', () => {
        if (syncEnabled && compressedVideo.src) {
            compressedVideo.currentTime = originalVideo.currentTime;
        }
    });

    // Sync compressed video events to original video
    compressedVideo.addEventListener('play', () => {
        if (syncEnabled && originalVideo.src && !originalVideo.paused) {
            return; // Don't sync if original is already playing
        }
        if (syncEnabled && originalVideo.src) {
            originalVideo.currentTime = compressedVideo.currentTime;
            originalVideo.play().catch(e => console.log('Sync play failed'));
        }
    });

    compressedVideo.addEventListener('pause', () => {
        if (syncEnabled && originalVideo.src) {
            originalVideo.pause();
        }
    });

    compressedVideo.addEventListener('seeked', () => {
        if (syncEnabled && originalVideo.src) {
            originalVideo.currentTime = compressedVideo.currentTime;
        }
    });

    // Show sync button when both videos are loaded
    function checkSyncButtonVisibility() {
        const bothVideosLoaded = originalVideo.src && compressedVideo.src;
        syncButton.style.display = bothVideosLoaded ? 'inline-block' : 'none';
    }

    // Monitor video loading
    originalVideo.addEventListener('loadeddata', checkSyncButtonVisibility);
    compressedVideo.addEventListener('loadeddata', checkSyncButtonVisibility);

    // Store sync state globally
    window.videoSyncEnabled = () => syncEnabled;
}
