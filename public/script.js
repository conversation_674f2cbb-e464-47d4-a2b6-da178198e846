// Initialize Socket.IO
const socket = io();

// Global variables
let currentVideo = null;
let currentCompressionJob = null;

// DOM elements
const fileInput = document.getElementById('fileInput');
const uploadArea = document.getElementById('uploadArea');
const s3Uri = document.getElementById('s3Uri');
const loadS3Btn = document.getElementById('loadS3Btn');
const refreshLocalBtn = document.getElementById('refreshLocalBtn');
const localFilesList = document.getElementById('localFilesList');
const originalVideo = document.getElementById('originalVideo');
const originalVideoInfo = document.getElementById('originalVideoInfo');
const originalVideoPlaceholder = document.getElementById('originalVideoPlaceholder');
const compressedVideo = document.getElementById('compressedVideo');
const compressedVideoInfo = document.getElementById('compressedVideoInfo');
const compressedVideoPlaceholder = document.getElementById('compressedVideoPlaceholder');
const runCompressionBtn = document.getElementById('runCompressionBtn');
const cancelCompressionBtn = document.getElementById('cancelCompressionBtn');
const compressionStatus = document.getElementById('compressionStatus');
const compressionProgress = document.getElementById('compressionProgress');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const compressionLogs = document.getElementById('compressionLogs');
const logsContent = document.getElementById('logsContent');
const refreshCompressedBtn = document.getElementById('refreshCompressedBtn');
const compressedFilesList = document.getElementById('compressedFilesList');

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
    loadLocalFiles();
    loadCompressedFiles();
});

// Event listeners
function setupEventListeners() {
    // File upload
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);

    // S3 input
    loadS3Btn.addEventListener('click', loadFromS3);
    s3Uri.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') loadFromS3();
    });

    // Local files
    refreshLocalBtn.addEventListener('click', loadLocalFiles);

    // Compression controls
    runCompressionBtn.addEventListener('click', startCompression);
    cancelCompressionBtn.addEventListener('click', cancelCompression);

    // Compressed files
    refreshCompressedBtn.addEventListener('click', loadCompressedFiles);
}

// Socket.IO event listeners
socket.on('compressionStatus', (status) => {
    updateCompressionStatus(status);
});

socket.on('compressionProgress', (data) => {
    updateProgress(data.progress);
});

socket.on('compressionComplete', (status) => {
    updateCompressionStatus(status);
    loadCompressedFiles();
    if (status.status === 'completed') {
        loadCompressedVideo(status.outputPath);
    }
});

socket.on('compressionError', (data) => {
    showError(`Compression failed: ${data.error}`);
});

// File upload handlers
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        uploadFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        uploadFile(file);
    }
}

// Upload file to server
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('video', file);

    try {
        showStatus('Uploading file...', 'running');
        const response = await fetch('/api/video/upload', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        if (result.success) {
            showStatus('File uploaded successfully!', 'completed');
            loadVideo(result.video);
            loadLocalFiles();
        } else {
            showError(result.error || 'Upload failed');
        }
    } catch (error) {
        showError(`Upload error: ${error.message}`);
    }
}

// Load video from S3
async function loadFromS3() {
    const uri = s3Uri.value.trim();
    if (!uri) {
        showError('Please enter an S3 URI');
        return;
    }

    try {
        showStatus('Loading from S3...', 'running');
        const response = await fetch('/api/video/s3', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ s3Uri: uri })
        });

        const result = await response.json();
        if (result.success) {
            showStatus('Video loaded from S3 successfully!', 'completed');
            loadVideo(result.video);
            s3Uri.value = '';
        } else {
            showError(result.error || 'S3 load failed');
        }
    } catch (error) {
        showError(`S3 error: ${error.message}`);
    }
}

// Load video into player
function loadVideo(videoInfo) {
    currentVideo = videoInfo;

    // Update video info
    const fileType = videoInfo.isY4M ? ' (Y4M - Original)' : '';
    const previewNote = videoInfo.isY4M && videoInfo.previewUrl ?
        '<div><strong>Note:</strong> Showing web-compatible preview. Original Y4M preserved for compression.</div>' : '';

    originalVideoInfo.innerHTML = `
        <div><strong>Filename:</strong> ${videoInfo.filename}${fileType}</div>
        <div><strong>Path:</strong> ${videoInfo.path}</div>
        ${videoInfo.size ? `<div><strong>Size:</strong> ${formatFileSize(videoInfo.size)}</div>` : ''}
        ${videoInfo.s3Uri ? `<div><strong>S3 URI:</strong> ${videoInfo.s3Uri}</div>` : ''}
        ${previewNote}
    `;

    // Load video - use preview URL for Y4M files, original URL for others
    const videoUrl = (videoInfo.isY4M && videoInfo.previewUrl) ? videoInfo.previewUrl : videoInfo.url;

    if (videoUrl) {
        originalVideo.src = videoUrl;
        originalVideo.style.display = 'block';
        originalVideoPlaceholder.style.display = 'none';
    } else if (videoInfo.isY4M) {
        // Y4M file without preview - show message
        originalVideoPlaceholder.innerHTML = `
            <div class="placeholder-content">
                <div class="placeholder-icon">🎬</div>
                <p>Y4M file loaded</p>
                <small>Preview generation in progress...</small>
            </div>
        `;
        originalVideo.style.display = 'none';
        originalVideoPlaceholder.style.display = 'flex';
    }

    // Enable compression button
    runCompressionBtn.disabled = false;
}

// Load compressed video
function loadCompressedVideo(outputPath) {
    const filename = outputPath.split('/').pop();
    const url = `/compressed/${filename}`;

    compressedVideoInfo.innerHTML = `
        <div><strong>Filename:</strong> ${filename}</div>
        <div><strong>Path:</strong> ${outputPath}</div>
    `;

    compressedVideo.src = url;
    compressedVideo.style.display = 'block';
    compressedVideoPlaceholder.style.display = 'none';
}

// Start compression
async function startCompression() {
    if (!currentVideo) {
        showError('No video loaded');
        return;
    }

    try {
        const response = await fetch('/api/compression/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                inputPath: currentVideo.path,
                isS3: !!currentVideo.s3Uri
            })
        });

        const result = await response.json();
        if (result.success) {
            currentCompressionJob = result.compressionId;
            runCompressionBtn.disabled = true;
            cancelCompressionBtn.disabled = false;
            showCompressionLogs();
        } else {
            showError(result.error || 'Failed to start compression');
        }
    } catch (error) {
        showError(`Compression error: ${error.message}`);
    }
}

// Cancel compression
async function cancelCompression() {
    if (!currentCompressionJob) return;

    try {
        const response = await fetch(`/api/compression/cancel/${currentCompressionJob}`, {
            method: 'POST'
        });

        const result = await response.json();
        if (result.success) {
            showStatus('Compression cancelled', 'failed');
            resetCompressionUI();
        }
    } catch (error) {
        showError(`Cancel error: ${error.message}`);
    }
}

// Load local files list
async function loadLocalFiles() {
    try {
        const response = await fetch('/api/video/list');
        const result = await response.json();

        if (result.success) {
            displayFilesList(result.videos, localFilesList, loadVideoFromList);
        }
    } catch (error) {
        console.error('Failed to load local files:', error);
    }
}

// Load compressed files list
async function loadCompressedFiles() {
    try {
        const response = await fetch('/api/compression/list');
        const result = await response.json();

        if (result.success) {
            displayFilesList(result.videos, compressedFilesList, loadCompressedFromList);
        }
    } catch (error) {
        console.error('Failed to load compressed files:', error);
    }
}

// Display files list
function displayFilesList(files, container, clickHandler) {
    if (files.length === 0) {
        container.innerHTML = '<div class="file-item">No files found</div>';
        return;
    }

    container.innerHTML = files.map(file => `
        <div class="file-item" data-file='${JSON.stringify(file)}'>
            <div class="file-name">${file.filename}</div>
            <div class="file-details">
                Size: ${formatFileSize(file.size)} |
                ${file.created ? `Created: ${new Date(file.created).toLocaleString()}` : ''}
            </div>
        </div>
    `).join('');

    // Add click handlers
    container.querySelectorAll('.file-item').forEach(item => {
        item.addEventListener('click', () => {
            const fileData = JSON.parse(item.dataset.file);
            clickHandler(fileData);
        });
    });
}

// Load video from list
function loadVideoFromList(fileData) {
    loadVideo({
        filename: fileData.filename,
        path: fileData.path,
        url: fileData.url,
        previewUrl: fileData.previewUrl,
        isY4M: fileData.isY4M,
        size: fileData.size
    });
}

// Load compressed video from list
function loadCompressedFromList(fileData) {
    loadCompressedVideo(fileData.path);
}

// Update compression status
function updateCompressionStatus(status) {
    const statusText = `Status: ${status.status.toUpperCase()}`;
    let statusClass = '';

    switch (status.status) {
        case 'running':
        case 'starting':
            statusClass = 'status-running';
            break;
        case 'completed':
            statusClass = 'status-completed';
            resetCompressionUI();
            break;
        case 'failed':
        case 'cancelled':
            statusClass = 'status-failed';
            resetCompressionUI();
            break;
    }

    compressionStatus.innerHTML = statusText;
    compressionStatus.className = `compression-status ${statusClass}`;

    if (status.status === 'running' || status.status === 'starting') {
        compressionProgress.style.display = 'block';
    }
}

// Update progress
function updateProgress(progress) {
    progressFill.style.width = `${progress}%`;
    progressText.textContent = `${progress}%`;
}

// Show compression logs
function showCompressionLogs() {
    compressionLogs.style.display = 'block';

    // Poll for logs
    const pollLogs = async () => {
        if (!currentCompressionJob) return;

        try {
            const response = await fetch(`/api/compression/logs/${currentCompressionJob}`);
            const result = await response.json();

            if (result.success) {
                displayLogs(result.logs);
            }
        } catch (error) {
            console.error('Failed to fetch logs:', error);
        }

        // Continue polling if job is active
        if (currentCompressionJob) {
            setTimeout(pollLogs, 2000);
        }
    };

    pollLogs();
}

// Display logs
function displayLogs(logs) {
    logsContent.innerHTML = logs.map(log => `
        <div class="log-entry">
            <span class="log-timestamp">${new Date(log.timestamp).toLocaleTimeString()}</span>
            <span class="log-message">${log.message}</span>
        </div>
    `).join('');

    // Scroll to bottom
    logsContent.scrollTop = logsContent.scrollHeight;
}

// Reset compression UI
function resetCompressionUI() {
    currentCompressionJob = null;
    runCompressionBtn.disabled = !currentVideo;
    cancelCompressionBtn.disabled = true;
    compressionProgress.style.display = 'none';
    progressFill.style.width = '0%';
    progressText.textContent = '0%';
}

// Utility functions
function showStatus(message, type) {
    compressionStatus.innerHTML = message;
    compressionStatus.className = `compression-status status-${type}`;
}

function showError(message) {
    compressionStatus.innerHTML = `Error: ${message}`;
    compressionStatus.className = 'compression-status status-failed';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
